"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.setGlobalTestRuntime = exports.getGlobalTestRuntimeWithAutoconfig = exports.getGlobalTestRuntime = void 0;
var _NoopTestRuntime = require("./NoopTestRuntime.js");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var ALLURE_TEST_RUNTIME_KEY = "allureTestRuntime";
var setGlobalTestRuntime = runtime => {
  globalThis[ALLURE_TEST_RUNTIME_KEY] = () => runtime;
};
exports.setGlobalTestRuntime = setGlobalTestRuntime;
var getGlobalTestRuntimeFunction = () => {
  return globalThis === null || globalThis === void 0 ? void 0 : globalThis[ALLURE_TEST_RUNTIME_KEY];
};
var getGlobalTestRuntime = () => {
  var testRuntime = getGlobalTestRuntimeFunction();
  if (testRuntime) {
    var _testRuntime;
    return (_testRuntime = testRuntime()) !== null && _testRuntime !== void 0 ? _testRuntime : _NoopTestRuntime.noopRuntime;
  }
  return _NoopTestRuntime.noopRuntime;
};
exports.getGlobalTestRuntime = getGlobalTestRuntime;
var getGlobalTestRuntimeWithAutoconfig = () => {
  var testRuntime = getGlobalTestRuntimeFunction();
  if (testRuntime) {
    var _testRuntime2;
    return (_testRuntime2 = testRuntime()) !== null && _testRuntime2 !== void 0 ? _testRuntime2 : _NoopTestRuntime.noopRuntime;
  }

  // protection from bundlers tree-shaking visiting (webpack, rollup)
  var pwAutoconfigModuleName = "allure-playwright/autoconfig";
  return (specifier => new Promise(r => r("".concat(specifier))).then(s => _interopRequireWildcard(require(s))))(pwAutoconfigModuleName).then(() => {
    var _getGlobalTestRuntime, _getGlobalTestRuntime2;
    return (_getGlobalTestRuntime = (_getGlobalTestRuntime2 = getGlobalTestRuntimeFunction()) === null || _getGlobalTestRuntime2 === void 0 ? void 0 : _getGlobalTestRuntime2()) !== null && _getGlobalTestRuntime !== void 0 ? _getGlobalTestRuntime : _NoopTestRuntime.noopRuntime;
  }).catch(() => _NoopTestRuntime.noopRuntime);
};
exports.getGlobalTestRuntimeWithAutoconfig = getGlobalTestRuntimeWithAutoconfig;
//# sourceMappingURL=runtime.js.map