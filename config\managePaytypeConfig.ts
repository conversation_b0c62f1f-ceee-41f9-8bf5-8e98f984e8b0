export class managePaytypeProjectName {
  static partsProject = `QA Aud-Rev Project: Parts R1`;
}

export class managePaytypeConfigData {
  static managePaytypePageHeader = "Manage Paytype";
  static auditModulePageHeader = "Audit Module";
  static noneValue = "None";
  static noLaborValue = "No Labor";
  static warrantyValue = "Warranty";
  static includeValue = "Include";
  static nonOEMValue = "Non-OEM";
  static partsValue = "Parts";
  static laborValue = "Labor";
  static excludedPaytypeValue = "Excluded Paytype";
  static paytypeCValue = "C";
  static swatValue = "SWAT";
  static exceptionAnalysisValue = "Exception Analysis";
  static copyDataValue = "Copy Data";
  static invoiceSequencesValue = "Invoice Sequences";
  static warrantyAnalysisValue = "Warranty Analysis";
  static mapperEditorValue = "Mapper Editor";
  static downloadROsValue = "Download ROs";
  static rangeSelectorValue = "Range Selector";
  static ruleDetailsValue = "Rule Details";
  static auditModuleValue = "Audit Module";
  static searchIcon = "Enter";
  static allValue = `All`;
  static departmentTooltip = `B - Bodyshop\nP - PDI\nS - Service`;
}
