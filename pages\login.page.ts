import { Page } from "@playwright/test";
import { LoginPageSection } from "../sections/login.section";

export default class LoginPage {
  page: Page;
  loginPageSection: LoginPageSection; // Corrected property name to refer to LoginPageSection

  constructor(page: Page) {
    this.page = page;
    this.loginPageSection = new LoginPageSection(this.page); // Instantiate LoginPageSection
  }

  public async login(username: string, password: string): Promise<void> {
    try {
      // Step-by-step login process
      await this.loginPageSection.enterUsername(username);
      await this.loginPageSection.enterPassword(password);
      await this.loginPageSection.clickOnLogInButton();
    } catch (error) {
      throw new Error(`An error occurred during the login process: ${error}`);
    }
  }
}
