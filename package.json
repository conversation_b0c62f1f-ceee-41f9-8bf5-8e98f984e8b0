{"name": "armatus", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"clean:allure": "<PERSON><PERSON>f allure-results allure-report", "test:allure": "npm run clean:allure && npx playwright test"}, "keywords": [], "author": "RJ", "license": "ARMATUS", "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^20.10.3", "allure-commandline": "^2.34.1", "allure-playwright": "^3.4.1", "playwright-html": "1.2.5", "rimraf": "^6.0.1", "tsx": "^4.19.2"}, "dependencies": {"axios": "^1.5.0", "dotenv": "^16.6.1", "exceljs": "^4.3.0", "i": "^0.3.7", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "pg": "^8.12.0", "playwright": "^1.50.1", "ts-node": "^10.9.2", "url-parse": "^1.5.10", "uuid": "^10.0.0", "winston": "^3.10.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "compilerOptions": {"lib": ["ES2022"], "module": "ES2022", "moduleResolution": "Node", "target": "ES2022", "esModuleInterop": true}}