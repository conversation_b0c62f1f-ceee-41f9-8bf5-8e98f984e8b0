{"version": 3, "file": "model.js", "names": ["Status", "exports", "StatusByPriority", "FAILED", "BROKEN", "PASSED", "SKIPPED", "Stage", "LabelName", "Severity", "ContentType", "LinkType"], "sources": ["../../src/model.ts"], "sourcesContent": ["export interface Attachment {\n  name: string;\n  type?: string;\n  source: string;\n  size?: number;\n}\n\nexport interface AttachmentOptions {\n  contentType: ContentType | string;\n  encoding?: BufferEncoding;\n  fileExtension?: string;\n  path?: string;\n  body?: Buffer;\n}\n\nexport interface Label {\n  name: LabelName | string;\n  value: string;\n}\n\nexport interface Link {\n  name?: string;\n  url: string;\n  type?: LinkType | string;\n}\n\nexport type ParameterMode = \"hidden\" | \"masked\" | \"default\";\n\nexport interface Parameter {\n  name: string;\n  value: string;\n  excluded?: boolean;\n  mode?: ParameterMode;\n}\n\nexport type ParameterOptions = Pick<Parameter, \"mode\" | \"excluded\">;\n\nexport interface StatusDetails {\n  message?: string;\n  trace?: string;\n  actual?: string;\n  expected?: string;\n}\n\n// don't use the interface as is, use Results types instead\ninterface Executable {\n  name?: string;\n  status?: Status;\n  statusDetails: StatusDetails;\n  stage: Stage;\n  description?: string;\n  descriptionHtml?: string;\n  steps: StepResult[];\n  attachments: Attachment[];\n  parameters: Parameter[];\n  start?: number;\n  stop?: number;\n}\n\nexport interface FixtureResult extends Executable {}\n\nexport interface StepResult extends Executable {\n  uuid?: string;\n}\n\nexport interface TestResult extends Executable {\n  uuid: string;\n  historyId?: string;\n  fullName?: string;\n  testCaseId?: string;\n  titlePath?: string[];\n  labels: Label[];\n  links: Link[];\n}\n\nexport interface TestResultContainer {\n  uuid: string;\n  name?: string;\n  children: string[];\n  befores: FixtureResult[];\n  afters: FixtureResult[];\n}\n\nexport type TestOrStepResult = StepResult | TestResult;\n\n/* eslint-disable no-shadow */\nexport enum Status {\n  FAILED = \"failed\",\n  BROKEN = \"broken\",\n  PASSED = \"passed\",\n  SKIPPED = \"skipped\",\n}\n\nexport const StatusByPriority = [Status.FAILED, Status.BROKEN, Status.PASSED, Status.SKIPPED];\n\n/* eslint-disable no-shadow */\nexport enum Stage {\n  SCHEDULED = \"scheduled\",\n  RUNNING = \"running\",\n  FINISHED = \"finished\",\n  PENDING = \"pending\",\n  INTERRUPTED = \"interrupted\",\n}\n\n/* eslint-disable no-shadow */\nexport enum LabelName {\n  ALLURE_ID = \"ALLURE_ID\",\n  /**\n   * @deprecated please use ALLURE_ID instead\n   */\n  AS_ID = ALLURE_ID,\n  SUITE = \"suite\",\n  PARENT_SUITE = \"parentSuite\",\n  SUB_SUITE = \"subSuite\",\n  EPIC = \"epic\",\n  FEATURE = \"feature\",\n  STORY = \"story\",\n  SEVERITY = \"severity\",\n  TAG = \"tag\",\n  OWNER = \"owner\",\n  LEAD = \"lead\",\n  HOST = \"host\",\n  THREAD = \"thread\",\n  TEST_METHOD = \"testMethod\",\n  TEST_CLASS = \"testClass\",\n  PACKAGE = \"package\",\n  FRAMEWORK = \"framework\",\n  LANGUAGE = \"language\",\n  LAYER = \"layer\",\n}\n\n/* eslint-disable no-shadow */\nexport enum Severity {\n  BLOCKER = \"blocker\",\n  CRITICAL = \"critical\",\n  NORMAL = \"normal\",\n  MINOR = \"minor\",\n  TRIVIAL = \"trivial\",\n}\n\n/* eslint-disable no-shadow */\nexport enum ContentType {\n  TEXT = \"text/plain\",\n  XML = \"application/xml\",\n  HTML = \"text/html\",\n  CSV = \"text/csv\",\n  TSV = \"text/tab-separated-values\",\n  CSS = \"text/css\",\n  URI = \"text/uri-list\",\n  SVG = \"image/svg+xml\",\n  PNG = \"image/png\",\n  JSON = \"application/json\",\n  ZIP = \"application/zip\",\n  WEBM = \"video/webm\",\n  JPEG = \"image/jpeg\",\n  MP4 = \"video/mp4\",\n  IMAGEDIFF = \"application/vnd.allure.image.diff\",\n}\n\n/* eslint-disable no-shadow */\nexport enum LinkType {\n  DEFAULT = \"link\",\n  ISSUE = \"issue\",\n  TMS = \"tms\",\n}\n\nexport interface ImageDiffAttachment {\n  expected: string | undefined; // data:image;base64,\n  actual: string | undefined; // data:image;base64,\n  diff: string | undefined; // data:image;base64,\n  name: string;\n}\n"], "mappings": ";;;;;;AA4CA;AAyCA;AAAA,IACYA,MAAM,GAAAC,OAAA,CAAAD,MAAA,0BAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAAA,OAANA,MAAM;AAAA;AAOX,IAAME,gBAAgB,GAAAD,OAAA,CAAAC,gBAAA,GAAG,CAACF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,MAAM,EAAEJ,MAAM,CAACK,MAAM,EAAEL,MAAM,CAACM,OAAO,CAAC;;AAE7F;AAAA,IACYC,KAAK,GAAAN,OAAA,CAAAM,KAAA,0BAALA,KAAK;EAALA,KAAK;EAALA,KAAK;EAALA,KAAK;EAALA,KAAK;EAALA,KAAK;EAAA,OAALA,KAAK;AAAA;AAQjB;AAAA,IACYC,SAAS,GAAAP,OAAA,CAAAO,SAAA,0BAATA,SAAS;EAATA,SAAS;EAEnB;AACF;AACA;EAJYA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAAA,OAATA,SAAS;AAAA;AA0BrB;AAAA,IACYC,QAAQ,GAAAR,OAAA,CAAAQ,QAAA,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;AAQpB;AAAA,IACYC,WAAW,GAAAT,OAAA,CAAAS,WAAA,0BAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;AAkBvB;AAAA,IACYC,QAAQ,GAAAV,OAAA,CAAAU,QAAA,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA", "ignoreList": []}