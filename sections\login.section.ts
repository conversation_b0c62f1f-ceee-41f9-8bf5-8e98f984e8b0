import { expect, Page } from "@playwright/test";
import Logger from "../utils/logger-util";
import { LoginPageSelectors as lps } from "../selectors/login.selectors";

const log = new Logger();

export class LoginPageSection {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async getPageTitle(): Promise<string> {
    return this.page.title();
  }

  async enterUsername(username: string): Promise<void> {
    try {
      await this.page.waitForSelector(lps.username);
      await this.page.fill(lps.username, username);
      log.info("Username entered successfully");
    } catch (error) {
      log.error(`An exception occurred while entering username: ${error}`);
      throw new Error(
        `An exception occurred while entering username: ${error}`
      );
    }
  }

  async enterPassword(password: string): Promise<void> {
    try {
      await this.page.waitForSelector(lps.password);
      await this.page.type(lps.password, password);
      log.info("Password entered successfully");
    } catch (error) {
      log.error(`An exception occurred while entering password: ${error}`);
      throw new Error(
        `An exception occurred while entering password: ${error}`
      );
    }
  }

  async clickOnLogInButton(): Promise<void> {
    try {
      await this.page.click(lps.loginButton);
      log.info("Clicked on Log-in button successfully");
    } catch (error) {
      log.error(
        `An exception occurred while clicking on Log-in button: ${error}`
      );
      throw new Error(
        `An exception occurred while clicking on Log-in button: ${error}`
      );
    }
  }
}
