# HLD Trigger Email Functionality Testing

## Overview

This repository contains an automated functionality testing solution for HLD Trigger Email. The framework is implemented in Node.js,Playwright and Typescript facilitating efficient testing based on specified criteria.

# Folder Structure

.
├── fixture
│ └── _.ts # Contains fixture definitions for test setup and teardown
├── pages
│ └── _.page.ts # Page Object Model (POM) classes for interacting with web pages
├── sections
│ └── _.section.ts # Classes representing sections or components within pages
├── selectors
│ └── _.selectors.ts # CSS and XPath selectors for elements within the web application
├── utils
│ └── _-utils.ts # Miscellaneous utility functions and classes used across the framework
├── tests
│ └── _.test.ts # Actual test scripts written using <PERSON><PERSON> for automation testing

## Installation

### Prerequisites

Before using this automation solution, ensure the following:

- **Node.js and npm:** Download and install Node.js and npm from [https://nodejs.org/](https://nodejs.org/).
- **Visual Studio Code (VS Code):** Recommended for managing and writing Playwright test scripts due to its robust development environment and relevant extensions.
- **Environment Variables:** Copy the `.env.sample` file, rename it `.env`, and update these variables:

```cmd
url=<HLD_TRIGGER_EMAIL_URL>
user=<LOGIN_USERNAME>
password=<LOGIN_PASSWORD>
partsProjectName=<PARTS_PROJECT_NAME>
laborProjectName=<LABOR_PROJECT_NAME>
browser=<BROWSER_NAME>


```

---

1. **Navigate to Repository:**

   ```bash
   cd "project name"
   ```

2. **Install Dependencies:**

   ```bash
   npm install
   ```

## Running Tests

## After Workspace Setup

1. Use the command in the terminal of VS Code of the root folder of the repository to install playwright browsers - `npx playwright install`.
2. Use the command in the terminal of VS Code of the root folder of the repository to install eslint - `npm install -g eslint`.
3. Use the command in the terminal of VS Code of the root folder of the repository to install prettier - `npm install -g prettier`.
4. Open VSC Extensions and search for `Prettier` and install `Prettier - Code Formatter` or `Prettier ESLint` extensions.
5. Open VSC user settings by clicking on the **Manage** icon in the bottom left corner of the VSC window and select `Settings`.
6. In the search bar at the top of the settings window, type `format on save` and select `Editor: Format On Save`.
7. Check the box to enable `Format On Save`. This will automatically format your code using the formatter extension you installed whenever you save a file.
8. Right click on the script file and then click on to : `Format Document With` -> `Configure Default Formatter` -> Select `Prettier Code Formatter`

## For Allure Report

1. For installing allure report use this command line`npm install --save-dev allure-playwright allure-commandline` in root folder of the project.
2. After execution, the `allure-results` folder will be added to the workspace.
3. Use this command in the terminal of VS code in the test directory - `npx allure generate allure-results --clean -o allure-report` to generate the report after test execution.
4. To view the report use `npx allure open allure-report` command will automatically open the results in webview in default browser.

## For Clearing Allure Report

1. For installing `rimraf` use this command line `npm install --save-dev rimraf` in root folder of the project.
2. Add a script in your package.json to clean the directories using rimraf: `"scripts": {"clean:allure": "rimraf allure-results allure-report","test:allure": "npm run clean:allure && npx playwright test"},`.
3. For clearing allure report use this command line `npm run clean:allure` in root folder of the project.

## DotEnv Setup

1. `npm install dotenv` Use this command in the terminal of VS Code to install dotenv package.
2. Add `import dotenv from "dotenv";` & `dotenv.config();` in the script inorder to use dotenv.

3. **Generate Cookies**
   Navigate to the root folder and Execute the following command for generating the cookies that required to skip the login process of each tests

   ```cmd

   npx tsx auth.ts

   ```

4. **Run Individual Test:**
   Navigate to the root folder and Execute the following command to run an individual test script:

   ```cmd
   npx playwright test <test_script_name> --headed
   ```

5. **Parallel Execution:**
   For parallel execution of tests, use the following command:

   ```cmd
   npx playwright test
   ```

## Additional Notes

- **Test Results:**
  After execution of tests results will be available in `playwright-report` folder and if we want show the report by using following command

  ```cmd
  npx playwright show-report
  ```

  For Allure reports preview execute the following command

  ```cmd
  allure serve
  ```

- **Dependency Installation:**
  Ensure all required dependencies are installed before running commands.
