{"uuid": "d08a85bd-2063-4054-8ef7-fa9ab33dd976", "name": "TC-16- Check whether the 'Override All Qual Jobs' functionality from the Discounts sub-tab in the Heuristic tab in the Rule Details UI is included in the Copy Data functionality.", "historyId": "3132c7c034d32b2690709a7f7ba67e9b:99b4c30d479cbf7bad99006b9cfd4915", "status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Launch browser", "start": 1758183059902, "uuid": "572bb740-1f25-4d6a-97b5-227626f4bed6", "stop": 1758183060680}], "attachments": [], "parameters": [], "name": "browserInstance", "start": 1758183059895, "uuid": "0f93aeb5-2411-415f-83bf-13f10238d887", "stop": 1758183060681}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Create context", "start": 1758183060684, "uuid": "af48c6b3-afda-426c-aad1-f5a1819bed2d", "stop": 1758183060701}], "attachments": [], "parameters": [], "name": "context", "start": 1758183060682, "uuid": "af248a44-588e-4457-8b85-a3bf813189ef", "stop": 1758183060702}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Create page", "start": 1758183060708, "uuid": "3cd2df2d-e125-4ebb-b2ec-e472b81b9864", "stop": 1758183060914}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Add cookies", "start": 1758183060915, "uuid": "be082638-0c6a-460e-a066-a92bbb4bb9dd", "stop": 1758183060919}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Navigate to \"/\"", "start": 1758183060924, "uuid": "e8d7076b-7d50-4dae-85ce-b6e59afef6c2", "stop": 1758183067483}], "attachments": [], "parameters": [], "name": "pageWithAuth", "start": 1758183060703, "uuid": "8c40f97c-d684-4a43-a135-b64dcba4e669", "stop": 1758183067483}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1758183059890, "uuid": "2e840d6f-6eb8-4dfa-984e-8835cddd28be", "stop": 1758183067484}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183067565, "name": "Wait for selector locator('//a[@href=\\'#/submission-summary\\']')", "uuid": "84b1cdba-3527-498e-b3c5-e08a7074b17b", "stop": 1758183073452}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183073457, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "1e6abbf2-8c22-4a8f-b7dd-6d56f4408cee", "stop": 1758183073461}], "attachments": [], "parameters": [], "start": 1758183073455, "name": "Check initial spinner visibility and count", "uuid": "51b64645-e89d-48e3-a456-f5f3a0a4ac73", "stop": 1758183073461}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183073463, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "6d1616e1-76fc-45b7-821b-8931efa78453", "stop": 1758183073467}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183073469, "name": "Is visible locator('//em[contains(@class, \\'fa fa-spinner\\')]').first()", "uuid": "ad9a9f76-f3d9-4de0-8ec5-1e8a357179f4", "stop": 1758183073472}], "attachments": [], "parameters": [], "start": 1758183073462, "name": "Wait for all spinners to disappear", "uuid": "b79c5e25-f2a6-4b80-a8a6-cfccbc5327e5", "stop": 1758183073473}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183073474, "name": "Wait for timeout", "uuid": "c6c4807d-1ba5-43bb-b286-a07ebe0ae442", "stop": 1758183074504}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074514, "name": "Wait for load state \"networkidle\"", "uuid": "f346d26f-9aa9-4de9-8ace-1fd57bea2d55", "stop": 1758183074517}], "attachments": [], "parameters": [], "start": 1758183073473, "name": "Wait additional time and ensure network is idle", "uuid": "018089c8-3e65-44a5-b46d-ab2245d119f4", "stop": 1758183074517}], "attachments": [], "parameters": [], "start": 1758183067562, "name": "Wait for Submission Summary <PERSON><PERSON> to be visible", "uuid": "b0dd0b3f-398f-46c0-9cdf-26dc607d2042", "stop": 1758183074517}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074526, "name": "Click locator('//a[@href=\\'#/submission-summary\\']')", "uuid": "95251d95-932c-4987-9ad9-b5dbad02b3d5", "stop": 1758183074580}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074581, "name": "Wait for selector locator('//input[@id=\\'inStartDate\\']')", "uuid": "546ad977-1a41-41d8-a173-e59d14248973", "stop": 1758183074595}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074598, "name": "Clear locator('//input[@id=\\'inStartDate\\']')", "uuid": "58625f73-96e1-4428-a1d2-5ff2aa636ac7", "stop": 1758183074649}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074652, "name": "Fill \"09-18-2024\" locator('//input[@id=\\'inStartDate\\']')", "uuid": "b51bdc7d-9e03-455b-a825-34e470f104b7", "stop": 1758183074661}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074662, "name": "Click locator('//input[@placeholder=\\'Search..\\']')", "uuid": "2a296264-bebc-4a92-83ce-95c01a82b04a", "stop": 1758183074746}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183074748, "name": "Type locator('//input[@placeholder=\\'Search..\\']')", "uuid": "796b633c-352e-4251-8802-58552d7319ca", "stop": 1758183075045}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183075046, "name": "Click locator('//em[@class=\\'fa fa-search\\']')", "uuid": "0b329647-c16a-4356-acbd-a7b9876d0aad", "stop": 1758183075129}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183075131, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "0054d327-f8ae-4ce0-8700-32f2a82a896b", "stop": 1758183075138}], "attachments": [], "parameters": [], "start": 1758183075129, "name": "Check initial spinner visibility and count", "uuid": "f1d3f2ad-32b7-4f47-9628-f9e123db714b", "stop": 1758183075138}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183075140, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "c840048b-9de7-4d1d-a72f-0a531997cd73", "stop": 1758183075145}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183075148, "name": "Is visible locator('//em[contains(@class, \\'fa fa-spinner\\')]').first()", "uuid": "275ca5e9-5023-419c-9a4f-8b6d5e40b312", "stop": 1758183075155}], "attachments": [], "parameters": [], "start": 1758183075138, "name": "Wait for all spinners to disappear", "uuid": "e21802dd-fa48-4247-a04e-068a6adbdde6", "stop": 1758183075155}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183075159, "name": "Wait for timeout", "uuid": "3db35834-1c34-4edd-a98f-b83b45e47133", "stop": 1758183076167}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183076169, "name": "Wait for load state \"networkidle\"", "uuid": "bbf969dc-f954-437f-8d1d-31cbeeab95ec", "stop": 1758183076170}], "attachments": [], "parameters": [], "start": 1758183075156, "name": "Wait additional time and ensure network is idle", "uuid": "0a5df05b-7aef-489d-a646-ffe11853e5d8", "stop": 1758183076171}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183076172, "name": "Click locator('xpath=(//div[@ref=\\'eViewport\\']//div[@col-id=\\'project_name\\']//span)[1]//em')", "uuid": "6a18a82b-4821-439a-845e-4f160e0c38c9", "stop": 1758183077063}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183077068, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "66006698-99f6-4dbd-8421-4fedaabb3749", "stop": 1758183077090}], "attachments": [], "parameters": [], "start": 1758183077065, "name": "Check initial spinner visibility and count", "uuid": "678f8e63-dc4d-4a9f-8850-90fc998acaed", "stop": 1758183077090}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183077099, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "700f951e-c8ed-4cb0-9a6d-3d03e4bb1cca", "stop": 1758183077122}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183077127, "name": "Is visible locator('//em[contains(@class, \\'fa fa-spinner\\')]').first()", "uuid": "f5fad425-fc42-405d-be0c-2282310a7257", "stop": 1758183077149}], "attachments": [], "parameters": [], "start": 1758183077093, "name": "Wait for all spinners to disappear", "uuid": "2cf79723-7c53-4556-88f1-31f3099b30f4", "stop": 1758183077149}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183077156, "name": "Wait for timeout", "uuid": "18f51361-0faf-4e29-a5c6-519475bf91ac", "stop": 1758183078169}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183078171, "name": "Wait for load state \"networkidle\"", "uuid": "39fb4154-5c9d-492d-86b8-27d5167d6520", "stop": 1758183078170}], "attachments": [], "parameters": [], "start": 1758183077152, "name": "Wait additional time and ensure network is idle", "uuid": "1ff0202b-1f49-464e-860e-f80496e1702c", "stop": 1758183078171}], "attachments": [], "parameters": [], "start": 1758183074519, "name": "Starting to navigate to Audit Mo<PERSON>le", "uuid": "c91b72f3-e639-453b-a512-54db58f2a4ef", "stop": 1758183078171}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183078173, "name": "Wait for timeout", "uuid": "68e822b9-157c-48fd-863a-8696103af5bc", "stop": 1758183081183}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183081196, "name": "Set viewport size", "uuid": "6fd2b5c2-eaa7-443b-8518-0cc6be7eacbb", "stop": 1758183081197}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183081197, "name": "Wait for selector locator('//div[@class=\\'project-label-value\\']//label[text()=\\' Project Name: \\']')", "uuid": "d24efbf0-2a68-4d28-86de-31193e614a1f", "stop": 1758183084121}], "attachments": [], "parameters": [], "start": 1758183067558, "name": "Starting test for navigating to Copy Data section", "uuid": "9af8879d-3d0d-49a6-a093-a654111c983e", "stop": 1758183084121}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084122, "name": "Click locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "b5417072-3468-4d24-8e93-cbbd478f7eae", "stop": 1758183084174}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084178, "name": "Wait for timeout", "uuid": "903cb420-7dc7-4192-8b1e-7bb4ed290f3f", "stop": 1758183084688}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084690, "name": "Click locator('//ul[@class=\\'dropdown-menu\\']//a[text()=\\'Copy Data\\']')", "uuid": "c1fc3ba2-dc6c-4fb7-a548-be0ad0b134bc", "stop": 1758183084806}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084815, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "04b90740-437a-4748-a62e-de76e8777115", "stop": 1758183084863}], "attachments": [], "parameters": [], "start": 1758183084811, "name": "Check initial spinner visibility and count", "uuid": "af9b7b09-c872-45e2-8f02-2e2e6ed0c271", "stop": 1758183084863}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084872, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "26c28341-e925-46f8-9275-aa738169cf20", "stop": 1758183084884}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084887, "name": "Is visible locator('//em[contains(@class, \\'fa fa-spinner\\')]').first()", "uuid": "9ad74181-ff49-4c75-93a2-67ad143fb389", "stop": 1758183084892}], "attachments": [], "parameters": [], "start": 1758183084865, "name": "Wait for all spinners to disappear", "uuid": "7c9bc0b2-e96f-4d60-b9c9-7f2b3a935213", "stop": 1758183084893}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183084900, "name": "Wait for timeout", "uuid": "ae6ed181-e851-4252-bc89-5bb7dbb8946b", "stop": 1758183085909}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183085910, "name": "Wait for load state \"networkidle\"", "uuid": "4ce2edeb-71f5-4986-a045-5bd1319d0507", "stop": 1758183085911}], "attachments": [], "parameters": [], "start": 1758183084896, "name": "Wait additional time and ensure network is idle", "uuid": "7fe65ef6-fb17-4a50-b3b9-a90f021db60a", "stop": 1758183085911}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183085912, "name": "Wait for timeout", "uuid": "020d93b7-6d07-48ed-97d8-3462d6f5bb16", "stop": 1758183090926}], "attachments": [], "parameters": [], "start": 1758183084121, "name": "🚀 Starting test for navigating to Copy Data section", "uuid": "7306f64d-7419-4374-bbb9-ce9a97c90593", "stop": 1758183090926}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183090933, "name": "Set viewport size", "uuid": "be85c65c-6c1e-41c3-bb54-bee557a9b481", "stop": 1758183090949}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183090934, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "641d63e2-d70f-44d1-b4e6-6c444f872e70", "stop": 1758183090952}], "attachments": [], "parameters": [], "start": 1758183090928, "name": "🚀 Verify Copy Data Page", "uuid": "f78f94e5-6a6a-4327-9c49-5d06c2ab9469", "stop": 1758183090952}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183090954, "name": "Click locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "45703c4c-2db0-4295-b934-8b6f516938cf", "stop": 1758183091013}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091014, "name": "Wait for timeout", "uuid": "48675601-dbf5-4e02-a241-c4a2729b729a", "stop": 1758183091517}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091519, "name": "Click locator('xpath=(//a[normalize-space()=\\'Go to Rule Details\\'])[1]')", "uuid": "98e34659-2fef-41e8-9210-df506bd427e1", "stop": 1758183091610}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091614, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "a12ed022-6448-4c37-ac5c-866f74f28665", "stop": 1758183091668}], "attachments": [], "parameters": [], "start": 1758183091612, "name": "Check initial spinner visibility and count", "uuid": "c89c9677-df08-44bb-8b1a-1260088a56b6", "stop": 1758183091668}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091676, "name": "Query count locator('//em[contains(@class, \\'fa fa-spinner\\')]')", "uuid": "c347aa01-d75c-477e-8a67-68cb7531505c", "stop": 1758183091689}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091692, "name": "Is visible locator('//em[contains(@class, \\'fa fa-spinner\\')]').first()", "uuid": "13800436-6528-49ee-8b39-b341456d65e0", "stop": 1758183091703}], "attachments": [], "parameters": [], "start": 1758183091672, "name": "Wait for all spinners to disappear", "uuid": "3385fd07-78e9-4caf-99d3-a734c562469b", "stop": 1758183091703}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183091706, "name": "Wait for timeout", "uuid": "c722d09f-6a95-4b9e-84ad-b1ba857173f8", "stop": 1758183092716}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183092719, "name": "Wait for load state \"networkidle\"", "uuid": "b9c317ae-b4e8-4b71-9c1d-65c1b2f490b7", "stop": 1758183092718}], "attachments": [], "parameters": [], "start": 1758183091704, "name": "Wait additional time and ensure network is idle", "uuid": "541684c5-6a2d-4c41-a32e-a1780bc80fb2", "stop": 1758183092719}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183092721, "name": "Wait for timeout", "uuid": "bdac1803-367c-490a-b468-d1570ed5002c", "stop": 1758183097722}], "attachments": [], "parameters": [], "start": 1758183090953, "name": "🚀 Starting test for navigating to Rule Details section", "uuid": "5a8b41e7-053a-4258-b3d5-df3655fedfb9", "stop": 1758183097722}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183097726, "name": "Set viewport size", "uuid": "b3860d61-9951-4ff0-bc15-c6b0fba8f3d0", "stop": 1758183097727}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183097728, "name": "Wait for selector locator('//span[contains(text(),\\'Rule Details\\')]')", "uuid": "79b43ea6-b2c6-45c2-901a-0d483a107e18", "stop": 1758183097752}], "attachments": [], "parameters": [], "start": 1758183097723, "name": "🚀 Verify Rule Details Page", "uuid": "b854e5c9-4428-450c-bcdb-d7a1216ad990", "stop": 1758183097754}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183097756, "name": "Wait for timeout", "uuid": "7c824593-3f57-42cd-ab29-05799480f688", "stop": 1758183100769}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183100771, "name": "Bring to front", "uuid": "782ae9ff-8558-4d75-b994-d03dfb61910d", "stop": 1758183100785}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183100786, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "3c952a5e-2017-429a-9b8c-02bcd0dfb3bb", "stop": 1758183100794}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183100795, "name": "Wait for timeout", "uuid": "f3977947-5182-45b2-8df2-d6a3de002673", "stop": 1758183103803}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183103806, "name": "Is visible locator('xpath=(//div[@class=\\'ag-center-cols-container\\'])[1][not(.//div[@role=\\'row\\'])]')", "uuid": "26dd449f-0db5-4230-bb5e-10ca2b535e9e", "stop": 1758183103808}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183103809, "name": "Wait for timeout", "uuid": "3c76dcca-2fb6-4c92-a6a1-83a819361b74", "stop": 1758183105816}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183105820, "name": "Hover locator('xpath=(//span[normalize-space()=\\'Store Name\\'])[1]')", "uuid": "1fb3ecdb-1c34-4213-9590-c78a4f47fceb", "stop": 1758183105849}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183105850, "name": "Wait for timeout", "uuid": "a1447501-ab0f-4c72-96ee-50ea2a7c05f3", "stop": 1758183106862}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183106864, "name": "Wait for selector locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "b4db7283-dda7-4ac3-8019-882b166dc05b", "stop": 1758183106868}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183106869, "name": "Click locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "b6eaaafd-365c-43ec-9934-5d675b1764be", "stop": 1758183106889}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183106891, "name": "Wait for timeout", "uuid": "712857fd-7c1b-4e21-94b0-151629cb0bc7", "stop": 1758183107898}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183107900, "name": "Wait for selector locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "83ed3589-**************-e978d18a3072", "stop": 1758183107912}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183107914, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "906ab316-b9f7-4087-af2a-32180f6d7630", "stop": 1758183107931}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183107932, "name": "Wait for timeout", "uuid": "c837d2ad-d58e-453e-9fe8-f3d2f6e194ed", "stop": 1758183108948}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183108950, "name": "Wait for selector locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "bbd4fd8b-0e04-44c8-bb50-fff2ebb316fc", "stop": 1758183108957}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183108958, "name": "Click locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "6af74508-4f0a-402c-a78a-fa687f53c774", "stop": 1758183108978}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183108979, "name": "Wait for timeout", "uuid": "bb4cad3c-8315-420d-8c41-5227a74e6398", "stop": 1758183109985}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183109987, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "ae4af854-7afa-432e-b146-221fd93c46ee", "stop": 1758183110000}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183110002, "name": "Wait for timeout", "uuid": "970f8986-0b0a-441a-8215-dded7c30de7c", "stop": 1758183111004}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111007, "name": "Get inner text locator('//div[@role=\\'gridcell\\' and @col-id=\\'submission_uuid\\']').first()", "uuid": "1ac878e7-d4b4-4f77-abeb-89a105285479", "stop": 1758183111011}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111012, "name": "Get inner text locator('//div[@role=\\'gridcell\\' and @col-id=\\'imported_on\\']/span').first()", "uuid": "83c58fe1-b153-4554-8f9c-f28a1d99acc9", "stop": 1758183111015}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111016, "name": "Bring to front", "uuid": "00fa7453-b7a1-4591-9f94-682df2d81f9c", "stop": 1758183111031}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111033, "name": "Wait for selector locator('//input[@placeholder=\\'Search..\\']')", "uuid": "9285a55d-678a-45a8-98db-ce585dd9627a", "stop": 1758183111045}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111047, "name": "Close", "uuid": "ff635ad2-a387-458e-a109-42b378e7962c", "stop": 1758183111070}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183111071, "name": "Wait for timeout", "uuid": "80ee4385-e81e-43a3-8f18-74f469e853a5", "stop": 1758183113088}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183113091, "name": "Bring to front", "uuid": "85eeb6ac-0aa8-4f58-a8b0-453ee922e942", "stop": 1758183113116}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183113117, "name": "Wait for selector locator('//input[@id=\\'inStartDate\\']')", "uuid": "b8dfe66b-4ac5-487c-9b03-3767c0fb2249", "stop": 1758183113130}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183113132, "name": "Wait for timeout", "uuid": "2063d7f8-2fa1-4e8e-9459-d73ddba0ea6a", "stop": 1758183115144}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183115146, "name": "Fill \"05-15-2025\" locator('xpath=(//input[@id=\\'inStartDate\\'])[1]')", "uuid": "b26aeef0-0c13-4566-ae7d-47bb344fe3f8", "stop": 1758183115175}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183115176, "name": "Fill \"05-15-2025\" locator('xpath=(//input[@id=\\'inEndDate\\'])[1]')", "uuid": "e552f941-55fe-4211-99d4-b1ec7e3f2053", "stop": 1758183115220}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183115221, "name": "Click locator('//button[normalize-space()=\\'Search\\']')", "uuid": "f2fcd022-09b6-4fc1-8bc9-1f4db6274efe", "stop": 1758183115300}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183115301, "name": "Wait for timeout", "uuid": "b49b3563-96ce-4e1a-94ce-4247e1f9d5e1", "stop": 1758183117315}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183117315, "name": "Fill \"c14f14dd-1539-4ad6-8c49-8c8c73f8bc18\" locator('xpath=(//input[@placeholder=\\'Search..\\'])[1]')", "uuid": "a035af1d-071c-48a1-895a-3c6046b23a26", "stop": 1758183117326}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183117329, "name": "Press \"Enter\"", "uuid": "212aae2e-11fb-4763-8547-12a73e5c9a57", "stop": 1758183117346}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183117347, "name": "Wait for timeout", "uuid": "9c8b82de-c348-4f26-90d0-a5664f7e91c4", "stop": 1758183119362}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183119363, "name": "Click locator('xpath=(//em[@title=\\'Go to Audit Module\\'])[1]').first()", "uuid": "6e5ea4d1-3ec4-44e1-b901-0203dd9f430e", "stop": 1758183119427}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183119430, "name": "Wait for timeout", "uuid": "a839f212-fd47-4060-a1fe-f24fe8e7b256", "stop": 1758183124441}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183124444, "name": "Wait for timeout", "uuid": "380656be-b272-4c96-bafa-026182b3dac5", "stop": 1758183126460}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183126463, "name": "Close", "uuid": "0e573169-836c-4b7e-a48f-57314388c4c8", "stop": 1758183126483}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183126485, "name": "Wait for timeout", "uuid": "ce3dbfaa-514a-4112-a447-4f72adf886ed", "stop": 1758183127501}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183127503, "name": "Click locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "c79ca726-e34f-4ba3-88a0-316cbc7e803d", "stop": 1758183127546}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183127547, "name": "Wait for timeout", "uuid": "3916a7ec-7a0a-48de-ac4a-10e31bb97b90", "stop": 1758183128550}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183128553, "name": "Click locator('xpath=(//a[normalize-space()=\\'Go to Rule Details\\'])[1]')", "uuid": "cb36cc7e-548a-42b2-bd9f-7223c2bffb99", "stop": 1758183128661}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183128662, "name": "Wait for timeout", "uuid": "797efa0f-6ad1-44a1-b9d4-f1036dcfad8f", "stop": 1758183133674}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183133676, "name": "Wait for timeout", "uuid": "5efe78d8-78b7-4371-ab74-a2b35d4b25a4", "stop": 1758183135691}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183135692, "name": "Wait for selector locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "fe7e6cc3-5a8a-4505-be66-039d8aa1f500", "stop": 1758183135708}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183135711, "name": "Wait for timeout", "uuid": "725178c4-1eaf-45b6-8632-fb8347bc4c0c", "stop": 1758183140725}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183140728, "name": "Is visible locator('xpath=(//label[normalize-space()=\\'Heuristic\\'])[1]')", "uuid": "f44ffe60-0be2-4547-994e-3c9859ad921c", "stop": 1758183140731}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183140732, "name": "Click locator('xpath=(//label[normalize-space()=\\'Heuristic\\'])[1]')", "uuid": "b72addf1-d709-4269-8083-a90e299ab399", "stop": 1758183140910}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183140911, "name": "Wait for timeout", "uuid": "afdf1c4a-b32a-4fed-ac71-4adddc2c83ca", "stop": 1758183143925}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183143926, "name": "Is visible locator('//label[normalize-space()=\\'Discounts\\']')", "uuid": "1b80a788-ce7f-4083-b690-3fcb06311f94", "stop": 1758183143931}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183143932, "name": "Click locator('//label[normalize-space()=\\'Discounts\\']')", "uuid": "b9e193da-9b41-475f-8f7c-37951e4986ad", "stop": 1758183144253}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183144255, "name": "Wait for timeout", "uuid": "153d1d12-0f1c-4af6-8a6f-6fdbdae8b689", "stop": 1758183147268}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183147269, "name": "Get inner text locator('xpath=(//ag-grid-angular//div[contains(@class,\\'ag-body-viewport\\') and @role=\\'presentation\\'])[2]//div[@col-id=\\'ro_number\\']//span').first()", "uuid": "4d645122-a5aa-46bd-afd1-6afe5e621b9e", "stop": 1758183147273}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183147274, "name": "Get inner text locator('xpath=(//div[@role=\\'row\\']//span[normalize-space(text())=\\'744663\\']/ancestor::div[@role=\\'row\\'])[1]//div[@col-id=\\'qualified_job_list\\']')", "uuid": "78b9d5f1-c3fe-48a6-8de7-a454bc1dc43d", "stop": 1758183147278}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183147278, "name": "Get inner text locator('xpath=(//div[@role=\\'row\\']//span[normalize-space(text())=\\'744663\\']/ancestor::div[@role=\\'row\\'])[1]//div[@col-id=\\'summary\\']')", "uuid": "f3e663ec-ca13-4636-980c-94ccb05ad02c", "stop": 1758183147284}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183147285, "name": "Click locator('xpath=(//span[@class=\\'ag-cell-value\\' and normalize-space()=\\'744663\\'])[1]')", "uuid": "2f78fe5a-e1b6-4d08-a712-931353db291d", "stop": 1758183147353}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183147354, "name": "Wait for timeout", "uuid": "a37f2e2a-9004-4539-811f-549210201e32", "stop": 1758183149370}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183149373, "name": "Click locator('//button[normalize-space()=\\'Override All Qual Jobs\\']')", "uuid": "5b5ce618-abb1-4bec-992b-ddaebb1c83b9", "stop": 1758183149412}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183149413, "name": "Wait for timeout", "uuid": "027273aa-d536-4391-8bae-7839c3d50197", "stop": 1758183150426}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183150427, "name": "Click locator('//div[@id=\\'allJobOverride\\']//div[@class=\\'c-btn\\']')", "uuid": "5261024a-0ad4-468d-aa29-cea6c3b1465b", "stop": 1758183150471}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183150472, "name": "Wait for timeout", "uuid": "89d117d2-f991-46d0-8348-86851cfacb2d", "stop": 1758183151493}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183151494, "name": "Click locator('xpath=(//label[contains(text(),\\'Exclude - Accessory\\')])[2]')", "uuid": "30fc4309-e285-4d60-85cd-9c138166dfbd", "stop": 1758183151540}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183151541, "name": "Wait for timeout", "uuid": "ea6f2dd3-4d69-43b6-a258-c5799de119af", "stop": 1758183152549}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183152550, "name": "Click locator('//div[@id=\\'allJobOverride\\']//button[@type=\\'button\\'][normalize-space()=\\'Override\\']')", "uuid": "ab72fb7d-b507-4a07-adc0-a89e0327154d", "stop": 1758183152586}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183152587, "name": "Wait for timeout", "uuid": "01210bd1-72db-4c2e-8df5-2eff82b4207b", "stop": 1758183154592}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183154594, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "4f947eb1-fdc8-43de-bc69-465d44edd6cc", "stop": 1758183154658}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183154661, "name": "toBeVisible", "uuid": "3fd94b6d-3ae7-4758-bbf4-a9b88ab640be", "stop": 1758183162685}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183162686, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "2f5d6479-c7cd-468e-9932-409506f4536c", "stop": 1758183162691}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183162693, "name": "Wait for timeout", "uuid": "89b83d68-efbd-4113-9e33-9027f14d42cc", "stop": 1758183164708}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183164709, "name": "Bring to front", "uuid": "dc2814c4-ad0d-4acd-b527-0b200aae335f", "stop": 1758183164721}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183164724, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "f1e85181-3b06-4292-a2da-b12f380c385f", "stop": 1758183164735}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183164738, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "425d1ae7-dc8d-4691-b11c-8162fe72357f", "stop": 1758183164787}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183164788, "name": "toBeVisible", "uuid": "c531d267-45d8-4ad1-98c2-27ba0aa39d31", "stop": 1758183172804}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183172807, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "ec6cb80c-0ecb-415c-9762-85773c1dcf99", "stop": 1758183172812}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183172815, "name": "Wait for timeout", "uuid": "c9fd038b-c5b5-482f-bb3b-1bc1525781f2", "stop": 1758183174817}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183174819, "name": "Reload", "uuid": "fc9451da-28fb-4bc6-aa92-33f331fa6761", "stop": 1758183175916}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183175917, "name": "Wait for timeout", "uuid": "e7876e1a-d512-4781-ab92-435ba5b5f4e7", "stop": 1758183180921}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183180922, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "e134d422-efb1-49de-bbaa-067cb1a0ad9d", "stop": 1758183180944}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183180945, "name": "Hover locator('xpath=(//span[normalize-space()=\\'Store Name\\'])[1]')", "uuid": "32b71b19-a8f6-4c26-a679-42e83c74567c", "stop": 1758183181210}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183181212, "name": "Wait for timeout", "uuid": "d550f61d-672f-49a2-8eaa-1fa854d22871", "stop": 1758183182223}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183182226, "name": "Wait for selector locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "211b0a6f-1608-4bb5-895f-eaacfc9a1714", "stop": 1758183182229}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183182230, "name": "Click locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "a79dac70-b855-4e83-865a-2b262a9696db", "stop": 1758183182251}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183182253, "name": "Wait for timeout", "uuid": "7813b41d-6d1b-4368-a0ca-c4ffc1ee2a0b", "stop": 1758183183259}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183183260, "name": "Wait for selector locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "5b340853-2922-4242-85f3-c45ddf15cfc6", "stop": 1758183183270}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183183271, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "4a435db9-5eed-43af-a740-ac972f6b7bf9", "stop": 1758183183285}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183183286, "name": "Wait for timeout", "uuid": "4efd5b3b-7f17-47e8-b684-2a2b288786cf", "stop": 1758183184290}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183184291, "name": "Wait for selector locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "c33a387f-0c8d-420b-a14e-f41b7949d089", "stop": 1758183184294}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183184296, "name": "Click locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "93683dfe-9e50-4125-a8bf-01d44dfcf7d4", "stop": 1758183184316}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183184317, "name": "Wait for timeout", "uuid": "6a30128c-e546-41a4-a120-039d1b669309", "stop": 1758183185327}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183185332, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "26128222-32b0-4f1d-9463-76acf5f48e70", "stop": 1758183185353}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183185354, "name": "Wait for timeout", "uuid": "a14073c5-4ab0-4e68-b92a-eb3f4bdb951e", "stop": 1758183186359}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183186361, "name": "Click locator('//div[@role=\\'gridcell\\' and @col-id=\\'submission_uuid\\' and normalize-space()=\\'c14f14dd-1539-4ad6-8c49-8c8c73f8bc18\\']')", "uuid": "89108090-e54d-4dad-b075-e186b8425de2", "stop": 1758183186393}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183186394, "name": "Click locator('//button[normalize-space()=\\'Copy\\']')", "uuid": "9bce8a91-a466-494d-a628-0653288cb2e9", "stop": 1758183186448}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183186449, "name": "Wait for timeout", "uuid": "76fc00a7-88c1-4fd4-a780-3f056fd56f3b", "stop": 1758183187463}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183187464, "name": "Wait for selector locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: rgb(1, 144, 254);\\']')", "uuid": "f5e14b9d-d6d1-456d-920e-65e14e0548da", "stop": 1758183187469}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183187470, "name": "Get inner text locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: rgb(1, 144, 254);\\']')", "uuid": "958106e6-3eb6-4a0c-a0fb-a1c905984a62", "stop": 1758183187472}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183187473, "name": "Wait for selector locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: green;\\']')", "uuid": "f880bcac-f57c-4bdd-966c-58d419c957d6", "stop": 1758183210454}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183210456, "name": "Get inner text locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: green;\\']')", "uuid": "9a742540-cf7a-49a7-baf0-bcb357feef59", "stop": 1758183210458}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183210459, "name": "Click locator('xpath=(//button[@class=\\'btn btn-secondary\\'])[1]')", "uuid": "831435bc-4d11-4297-85bd-ff9773b4085b", "stop": 1758183210495}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183210495, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "8a364c69-190c-4dfe-a232-90976e029ca2", "stop": 1758183210898}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183210899, "name": "toBeVisible", "uuid": "04bc3310-ea09-4d62-8fd7-d3af28a6b7a6", "stop": 1758183218861}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183218862, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "8ff2e57b-37f2-4901-92ed-899580fa8ca8", "stop": 1758183218864}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183218866, "name": "Wait for timeout", "uuid": "5a79e1ce-b6af-415c-b869-51b0539e7bf4", "stop": 1758183220894}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183220897, "name": "Click locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "b8f64c90-b58b-4f67-a6a1-35793de27b6a", "stop": 1758183220921}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183220922, "name": "Wait for timeout", "uuid": "9cf0c89c-dcfe-45e9-b73f-19d3db674860", "stop": 1758183221926}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183221927, "name": "Click locator('//ul[@class=\\'dropdown-menu\\']//a[text()=\\'Go to Audit Module\\']')", "uuid": "9cd365da-4dc1-492d-8b2d-df96e472003e", "stop": 1758183222002}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183222004, "name": "Wait for timeout", "uuid": "0e406d8f-df77-46d4-bdbf-2b47455f7248", "stop": 1758183227019}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183227022, "name": "Reload", "uuid": "db8a0445-fa31-4858-9c6a-7dae9671f0e6", "stop": 1758183227929}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183227931, "name": "Wait for timeout", "uuid": "aca92d4c-ff2a-486f-be43-91c5b70b9913", "stop": 1758183232941}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183232943, "name": "Select option locator('xpath=(//select[@id=\\'page-size\\'])[1]')", "uuid": "bf148508-250f-418f-9126-4abc244873ca", "stop": 1758183232964}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183232965, "name": "Click locator('xpath=(//button[normalize-space()=\\'Refresh List\\'])[1]')", "uuid": "779fd0dc-b412-4994-97bb-d7c00d638e40", "stop": 1758183233011}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183233012, "name": "Wait for timeout", "uuid": "340afe0f-64d2-46d7-bffc-c95e09156657", "stop": 1758183238019}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183238020, "name": "Click locator('xpath=(//a[normalize-space()=\\'All\\'])[1]')", "uuid": "e12181e3-593a-4aa7-88c2-41d4b5726efd", "stop": 1758183238100}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183238101, "name": "Fill \"744663\" locator('xpath=(//input[@placeholder=\\'Search..\\'])[1]')", "uuid": "65a24958-ba58-4e32-8286-79b91abe130f", "stop": 1758183238115}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183238116, "name": "Press \"Enter\"", "uuid": "78bba11e-1e54-4d92-b5ae-31deccf6ab94", "stop": 1758183238122}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183238122, "name": "Wait for timeout", "uuid": "bdd595dc-d4d3-4c09-8a1c-2d82eebc7e3c", "stop": 1758183241130}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183241133, "name": "Click locator('//div[@col-id=\\'ro_number\\']//span[normalize-space()=\\'744663\\']').first()", "uuid": "0abd80a8-f1ea-4220-8f69-5dc941b6db21", "stop": 1758183241212}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183241213, "name": "Wait for timeout", "uuid": "c6e27faa-560f-4173-a9b8-db9ef3f91603", "stop": 1758183244215}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244216, "name": "Evaluate locator('//div[@role=\\'gridcell\\' and normalize-space()=\\'A1\\']/parent::div//div[@col-id=\\'job_number\\']')", "uuid": "6a2a1876-3eb3-4ad9-a4e1-f16569f97662", "stop": 1758183244237}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244239, "name": "Evaluate locator('//div[@role=\\'gridcell\\' and normalize-space()=\\'B1\\']/parent::div//div[@col-id=\\'job_number\\']')", "uuid": "5c731840-f568-4716-8b01-f6c79ba93284", "stop": 1758183244247}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244250, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'final_outcome\\']')", "uuid": "4b9cd24e-eb5d-46c2-8dfb-448f6c8296be", "stop": 1758183244277}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244279, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'override\\']')", "uuid": "a259e803-733c-42a5-858c-5f5d1d11eac7", "stop": 1758183244303}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244306, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'job_outcome\\']')", "uuid": "21e3d770-7be3-4faf-8f28-ffa7ad918762", "stop": 1758183244329}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244331, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'job_outcome_reason\\']')", "uuid": "45af00bb-c4bd-460d-ad3c-b4769fda64c7", "stop": 1758183244351}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244355, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'job_override\\']')", "uuid": "ac21f270-cb79-416a-93a9-2a012f36acde", "stop": 1758183244382}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244384, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'job_override_reason\\']')", "uuid": "c8f0407b-bc22-4f1c-bc8e-02ee3dd40ead", "stop": 1758183244409}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244412, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'job_override\\']').first()", "uuid": "2fd5eaf8-0b8c-4f94-8fb3-db651b739ed3", "stop": 1758183244441}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244444, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'final_outcome\\']').nth(1)", "uuid": "c14b9317-1f84-49f4-8ad5-107f163e743f", "stop": 1758183244480}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244481, "name": "Evaluate locator('//div[contains(@class,\\'ag-row\\')]//div[@col-id=\\'override\\']').nth(1)", "uuid": "d727dd2e-2407-4e1a-8362-21c289bab56f", "stop": 1758183244513}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244515, "name": "Bring to front", "uuid": "4bef3bfc-**************-e7b56c8239ce", "stop": 1758183244547}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244554, "name": "Wait for selector locator('//button[@data-toggle=\\'dropdown\\'and text()=\\' Utilities \\']')", "uuid": "2fc85245-a698-46ab-a4f4-af17464ce6e9", "stop": 1758183244614}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183244617, "name": "Wait for timeout", "uuid": "453a8987-3977-40ae-822f-c54a22e23be6", "stop": 1758183247629}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183247630, "name": "Is visible locator('xpath=(//label[normalize-space()=\\'Heuristic\\'])[1]')", "uuid": "bf1ae32d-1aa4-43be-9b4a-14fe69823009", "stop": 1758183247632}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183247633, "name": "Click locator('xpath=(//label[normalize-space()=\\'Heuristic\\'])[1]')", "uuid": "3fe300fc-577d-4c4a-8be8-ae796f43510e", "stop": 1758183247768}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183247769, "name": "Wait for timeout", "uuid": "8a0162db-d548-492a-86e0-2b3dc8a28003", "stop": 1758183252800}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183252803, "name": "Is visible locator('//label[normalize-space()=\\'Discounts\\']')", "uuid": "8dd6d3f1-038a-4fe8-b1a9-99216dcaf304", "stop": 1758183252806}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183252806, "name": "Click locator('//label[normalize-space()=\\'Discounts\\']')", "uuid": "3b2fe8e9-de9b-43ad-b662-5fdb79717d4d", "stop": 1758183252944}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183252945, "name": "Wait for timeout", "uuid": "2364535b-c737-46d4-9b9e-c454696bc158", "stop": 1758183255956}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183255958, "name": "Click locator('xpath=(//span[@class=\\'ag-cell-value\\' and normalize-space()=\\'744663\\'])[1]')", "uuid": "69ca0c18-cdd8-4d9f-b79b-c0e1837202ed", "stop": 1758183256020}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183256021, "name": "Wait for timeout", "uuid": "31966517-97c4-44cb-ab7a-7450ef91318f", "stop": 1758183258034}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183258035, "name": "Click locator('//button[normalize-space()=\\'Override All Qual Jobs\\']')", "uuid": "f0d4446c-2559-4b8c-bce0-192554020b10", "stop": 1758183258084}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183258085, "name": "Wait for timeout", "uuid": "f7577dc3-026e-4d66-bd1b-75b8a0a7b510", "stop": 1758183259096}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183259100, "name": "Click locator('//div[@id=\\'allJobOverride\\']//div[@class=\\'c-btn\\']')", "uuid": "7f2bada0-1a98-4cf8-9a18-091e5d679c95", "stop": 1758183259143}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183259144, "name": "Wait for timeout", "uuid": "1529bc10-335a-4821-adf2-87e5c0f8f22b", "stop": 1758183260144}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183260146, "name": "Click locator('xpath=(//label[contains(text(),\\'None\\')])[2]')", "uuid": "2ffdb4f2-674e-43d4-90d9-d730ef11b2a3", "stop": 1758183260188}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183260189, "name": "Wait for timeout", "uuid": "d1f00252-c1cc-4f2c-89ae-f59a3d8e730e", "stop": 1758183261205}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183261207, "name": "Click locator('//div[@id=\\'allJobOverride\\']//button[@type=\\'button\\'][normalize-space()=\\'Override\\']')", "uuid": "22e8e68f-93dd-4942-9f3d-************", "stop": 1758183261248}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183261250, "name": "Wait for timeout", "uuid": "dcaa1b99-cbe6-411c-98f5-85a437f55846", "stop": 1758183263253}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183263254, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "b5a1b4f0-ae0d-4a9a-9d3f-51caca7df5b6", "stop": 1758183263298}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183263299, "name": "toBeVisible", "uuid": "e18291c8-e229-4b60-8435-c766ea05a8f6", "stop": 1758183271337}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183271338, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "16d0d530-e928-4ad9-b5ea-ff0d7aee8a6f", "stop": 1758183271344}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183271345, "name": "Wait for timeout", "uuid": "6d61d0af-ef26-4d2e-9476-f8a9fb3fc3b3", "stop": 1758183273347}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183273350, "name": "Bring to front", "uuid": "84389165-90af-49a9-983e-c240d3d3ddc7", "stop": 1758183273366}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183273367, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "e1af18f8-3388-4eb7-a0f8-69e4a582940e", "stop": 1758183273374}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183273376, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "3b0406e5-1864-4b96-a732-4be04cabe309", "stop": 1758183273414}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183273415, "name": "toBeVisible", "uuid": "b4990c18-6da9-4b97-87d4-6e1d777c8edb", "stop": 1758183281424}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183281427, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "0e89dff0-8aa7-4635-ac6b-19c09c7007c6", "stop": 1758183281429}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183281430, "name": "Wait for timeout", "uuid": "12385253-1ae4-4068-9583-08e0a01e2646", "stop": 1758183283443}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183283445, "name": "Reload", "uuid": "2149e577-5207-42b8-b17f-198cc5bbee35", "stop": 1758183284441}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183284443, "name": "Wait for timeout", "uuid": "2755347f-7c9b-4d31-b9d4-e4493f87b30c", "stop": 1758183289446}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183289449, "name": "Wait for selector locator('//span[contains(text(),\\'Copy Data\\')]')", "uuid": "83472594-e18a-467e-8167-77bb37f7a954", "stop": 1758183289469}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183289470, "name": "Wait for timeout", "uuid": "f93fb30d-3bb1-4c5e-8af5-ef9e3262844c", "stop": 1758183294472}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183294475, "name": "Hover locator('xpath=(//span[normalize-space()=\\'Store Name\\'])[1]')", "uuid": "a9901b06-04e9-4ab8-bbab-6b96443b011c", "stop": 1758183294506}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183294507, "name": "Wait for timeout", "uuid": "f7511c92-a5df-473c-85b0-c99ae37d71ad", "stop": 1758183295518}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183295519, "name": "Wait for selector locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "d992eb2c-c5a0-43ac-90fb-c9c02d8e834b", "stop": 1758183295523}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183295524, "name": "Click locator('//div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]//span[text()=\\'Store Name\\']\\n     /ancestor::div[contains(@class,\\'ag-header-cell-comp-wrapper\\')]\\n     //span[contains(@class,\\'ag-header-cell-menu-button\\')]')", "uuid": "5442b4e0-4512-4217-a408-c9a1da6a7fe3", "stop": 1758183295548}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183295550, "name": "Wait for timeout", "uuid": "df5443b5-abc3-4801-a327-056ae1a14e6e", "stop": 1758183296553}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183296557, "name": "Wait for selector locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "5e27e800-ee9c-42dc-8b4d-35ddf106a84a", "stop": 1758183296562}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183296563, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "a2d4d40f-d122-4b33-b07e-c67c5ab32d14", "stop": 1758183296580}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183296581, "name": "Wait for timeout", "uuid": "5cf0cdf2-fc3f-456a-a5eb-eafd017b2636", "stop": 1758183297588}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183297590, "name": "Wait for selector locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "0fb9e8e3-71e3-47a9-8746-22b971ec73a0", "stop": 1758183297596}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183297596, "name": "Click locator('//span[normalize-space()=\\'Submission UUID\\']/../..//input[@type=\\'checkbox\\']')", "uuid": "20c3aff4-2488-411b-9fa7-53ebd5d18aa9", "stop": 1758183297614}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183297617, "name": "Wait for timeout", "uuid": "2b2c15f7-09e3-4ae7-9a75-446ad3bff2a2", "stop": 1758183298622}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183298624, "name": "Click locator('xpath=(//span[@role=\\'tab\\' and @aria-label=\\'columns\\']//span[contains(@class,\\'ag-icon-columns\\')])[1]')", "uuid": "3b5f12b5-d137-477d-a8d3-d1d67eadb5bd", "stop": 1758183298650}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183298651, "name": "Wait for timeout", "uuid": "7aa2034e-d5ec-4d3e-beeb-4536a65b718f", "stop": 1758183299655}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183299657, "name": "Click locator('//div[@role=\\'gridcell\\' and @col-id=\\'submission_uuid\\' and normalize-space()=\\'c14f14dd-1539-4ad6-8c49-8c8c73f8bc18\\']')", "uuid": "01e69647-ab86-44fe-a8de-9917e1a53eef", "stop": 1758183299690}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183299692, "name": "Click locator('//button[normalize-space()=\\'Copy\\']')", "uuid": "914ad88e-0c95-4c28-9cb6-c754456a0057", "stop": 1758183299765}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183299767, "name": "Wait for timeout", "uuid": "e3a55f99-6cc7-4d45-8f59-89db6fd5cf0f", "stop": 1758183300776}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183300777, "name": "Wait for selector locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: rgb(1, 144, 254);\\']')", "uuid": "46f5124e-0aaf-4b1f-af45-a70d0b0c4688", "stop": 1758183300780}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183300781, "name": "Get inner text locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: rgb(1, 144, 254);\\']')", "uuid": "8a2203cb-05a4-4eeb-99b9-85b19f3a216e", "stop": 1758183300783}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183300783, "name": "Wait for selector locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: green;\\']')", "uuid": "9f50c50e-0d9b-44e9-99bd-20e31675ebdb", "stop": 1758183322798}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183322801, "name": "Get inner text locator('//div[@class=\\'row\\']//label[@for=\\'copy_status\\']//span[@style=\\'color: green;\\']')", "uuid": "938e482e-0056-4987-a17c-76fd64b36c1e", "stop": 1758183322807}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183322812, "name": "Click locator('xpath=(//button[@class=\\'btn btn-secondary\\'])[1]')", "uuid": "92bf26e0-a71c-443d-9fdf-834e506ee2fb", "stop": 1758183322851}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183322852, "name": "Click locator('xpath=(//button[normalize-space()=\\'Update\\'])[1]')", "uuid": "1585f6d4-b0f3-4abe-bb79-774f02ab8aeb", "stop": 1758183323296}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183323298, "name": "toBeVisible", "uuid": "9936342b-bec5-491b-8d9e-2740ed29a78e", "stop": 1758183331238}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183331240, "name": "Get text content locator('//div[@role=\\'alert\\' and contains(text(),\\'Updated Outcome successfully\\')]')", "uuid": "12b94b62-4884-4935-958a-97076ff2c695", "stop": 1758183331242}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1758183331243, "name": "Wait for timeout", "uuid": "7136546a-4063-4f66-a1b5-78b6bdb3bfce", "stop": 1758183333251}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "pageWithAuth", "start": 1758183333256, "uuid": "558e8e15-c0bc-4b10-82f7-b1c1a46f2b2a", "stop": 1758183333256}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Close context", "start": 1758183338269, "uuid": "a69a6d8a-d23e-4a42-b7d3-18425678bee4", "stop": 1758183338345}], "attachments": [], "parameters": [], "name": "context", "start": 1758183333256, "uuid": "d919ee69-aacb-41b2-9f73-4d791f099382", "stop": 1758183338345}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserInstance", "start": 1758183338345, "uuid": "0aaa5b75-6972-4f14-898b-fdca090f23d7", "stop": 1758183338345}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Close browser", "start": 1758183338356, "uuid": "0020a990-2fff-47c7-a321-f407e976fab5", "stop": 1758183338599}], "attachments": [], "parameters": [], "name": "afterAll hook", "start": 1758183338348, "uuid": "a5e987bf-42cd-49b7-88f8-f6ff850b62e3", "stop": 1758183338600}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1758183333253, "uuid": "f56b4561-d24d-48dd-adf1-69d970638776", "stop": 1758183338600}], "attachments": [{"name": "stdout", "source": "f3177fd9-d5f1-434c-a73a-93981fd7be75-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "Project", "value": "ARMATUS AUDIT"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "copyData.test.ts"}, {"name": "titlePath", "value": " > ARMATUS AUDIT > copyData.test.ts"}, {"name": "host", "value": "AKHIL-SASTHA-CITRUS"}, {"name": "thread", "value": "pid-20968-worker-0"}, {"name": "parentSuite", "value": "ARMATUS AUDIT"}, {"name": "suite", "value": "copyData.test.ts"}], "links": [], "start": 1758183059907, "testCaseId": "3132c7c034d32b2690709a7f7ba67e9b", "fullName": "copyData.test.ts:105:6", "titlePath": ["copyData.test.ts"], "stop": 1758183338360}