import test from "../fixture/fixtures";
import Logger from "../utils/logger-util";
import dotenv from "dotenv";
import LoginPage from "../pages/login.page";

dotenv.config();

const USERNAME = process.env.user || "";
const PASSWORD = process.env.password || "";

const logger = new Logger();

test.describe("Login and Search Functionalities", () => {
  test.beforeEach(async ({ pageWithAuth }) => {
    if (!pageWithAuth) {
      throw new Error("pageWithAuth is undefined. Check fixture setup.");
    }
    logger.info("Starting a new test for Login functionalities");
  });
});
