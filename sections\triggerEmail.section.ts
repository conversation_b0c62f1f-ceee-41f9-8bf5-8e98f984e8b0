import { expect, Page, test } from "@playwright/test";
import Logger from "../utils/logger-util.ts";
import { triggerEmailSelectors as cpt } from "../selectors/triggerEmail.selectors.ts";
import UtilSection from "../utils/baseFunction.utils.ts";
import { copyDataConfigData as cfg } from "../config/copyDataConfig.ts";
import { projectNameConfigData as png } from "../config/copyDataConfig.ts";

const logger = new Logger();
const errors: string[] = [];

const partsProject: string = png.partsProject || "";
const newPartsProject: string = png.newPartsProject || "";

export class CopyDataSection {
  page: Page;
  util: UtilSection;

  constructor(page: Page) {
    this.page = page;
    this.util = new UtilSection(page);
  }

  public async WaitForLoadingSpinner() {
    const selector = cpt.loaderSpinner; // Flexible locator

    try {
      await test.step("Check initial loading spinner visibility", async () => {
        const loadingEl = this.page.locator(selector);
        await expect(loadingEl).toBeVisible();
        logger.info("✅ Loading spinner is visible");
      });

      await test.step("Wait for dashboard text to be visible", async () => {
        const dashboardHeader = this.page.locator(cpt.dashboardHeader);
        await expect(dashboardHeader).toHaveText("Dashboard", {
          timeout: 90000,
        });
        logger.info("✅ Dashboard text is visible");
      });
    } catch (error: any) {
      logger.error(`❌ Error during WaitForLoadingSpinner: ${error.message}`);
      throw error;
    }
  }

  // CD_01 - Check whether the Manage Users UI is navigated
  public async navigateToManageUsersFunctionality() {
    try {
      // Step 1: Wait for the page to finish loading
      await this.WaitForLoadingSpinner();

      await this.page.waitForTimeout(2000);

      await this.page.click(cpt.utilitiesDropdown);

      // Step 2: Click on Manage Users menu option (
      await test.step("Navigate to Manage Users", async () => {
        await this.page.click(cpt.manageUsersMenu);
        logger.info("✅ Clicked on Manage Users menu");
      });

      // Step 3: Verify Manage Users header is visible
      await test.step("Verify Manage Users header", async () => {
        const header = this.page.locator(cpt.manageUsersHeader);
        await expect(header).toHaveText("Manage Users", { timeout: 30000 });
        logger.info("✅ Manage Users page is visible");
      });
    } catch (error: any) {
      logger.error(
        `❌ Error during navigateToManageUsersFunctionality: ${error.message}`
      );
      throw error;
    }
  }
}
