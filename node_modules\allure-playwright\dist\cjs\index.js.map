{"version": 3, "file": "index.js", "names": ["_nodeFs", "require", "_nodePath", "_interopRequireDefault", "_nodeProcess", "_allure<PERSON><PERSON><PERSON><PERSON><PERSON>", "_sdk", "_reporter", "_legacy", "_utils", "_test", "e", "__esModule", "_regenerator", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_createForOfIteratorHelper", "Array", "isArray", "_unsupportedIterableToArray", "_n", "F", "s", "next", "_toArray", "_arrayWithHoles", "_iterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_nonIterableSpread", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_classPrivateMethodInitSpec", "_checkPrivateRedeclaration", "add", "has", "_toPrimitive", "_typeof", "toPrimitive", "String", "Number", "_assert<PERSON>lassBrand", "_AllureReporter_brand", "WeakSet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "config", "Date", "Map", "options", "suiteTitle", "detail", "onConfigure", "testPlan", "parseTestPlan", "configElement", "testsWithSelectors", "tests", "selector", "v1ReporterTests", "v2ReporterTests", "cli<PERSON><PERSON>s", "selectorToGrepPattern", "escapeRegExp", "path", "normalize", "concat", "v2SelectorsArgs", "map", "replace", "v1SelectorsArgs", "split", "onError", "onExit", "onStdErr", "onStdOut", "onBegin", "suite", "allureRuntime", "ReporterRuntime", "writer", "createDefaultWriter", "resultsDir", "onTestBegin", "parent", "titleMetadata", "extractMetadataFromString", "title", "project", "testFile<PERSON><PERSON>", "relative", "testDir", "location", "file", "relativeFileParts", "sep", "relativeFile", "join", "_suite$titlePath", "titlePath", "_suite$titlePath2", "suiteTitles", "nameSuites", "testCaseIdBase", "result", "cleanTitle", "labels", "getEnvironmentLabels", "links", "parameters", "steps", "testCaseId", "md5", "fullName", "line", "column", "getLanguageLabel", "getFrameworkLabel", "getPackageLabel", "_ref", "tags", "tag", "LabelName", "TAG", "startsWith", "substring", "_iterator", "annotations", "_step", "annotation", "type", "_this$options$links", "formatLink", "LinkType", "ISSUE", "url", "description", "_this$options$links2", "TMS", "annotationLabel", "getMetadataLabel", "status", "Status", "PASSED", "stage", "Stage", "FINISHED", "attachments", "statusDetails", "err", "repeatEach", "repeatEachIndex", "testUuid", "startTest", "allureResultsUuids", "set", "id", "startedTestCasesTitlesCache", "onStepBegin", "_result", "step", "isRootBeforeHook", "BEFORE_HOOKS_ROOT_STEP_TITLE", "isRootAfterHook", "AFTER_HOOKS_ROOT_STEP_TITLE", "isRootHook", "isBeforeHookDescendant", "isBeforeHookStep", "isAfterHookDescendant", "isAfterHookStep", "isHookStep", "get", "includes", "category", "_this$allureRuntime", "_this$attachmentSteps", "currentStep", "attachmentSteps", "_shouldIgnoreStep", "baseStep", "createStepResult", "start", "startTime", "getTime", "RUNNING", "uuid", "randomUuid", "stack", "beforeHooksStepsStack", "afterHooksStepsStack", "startStep", "attachStack", "beforeHooksAttachmentsStack", "afterHooksAttachmentsStack", "updateStep", "step<PERSON><PERSON><PERSON>", "_attachStack$get", "normalizeHookTitle", "stopStep", "ShallowStepsStack", "undefined", "onStepEnd", "isAfterHook", "isHook", "_getWorstTestStepResu", "_ref2", "getWorstTestStepResult", "_ref2$status", "error", "FAILED", "getMessageAndTraceFromError", "duration", "_getWorstTestStepResu2", "_ref3", "_ref3$status", "_onTestEnd", "_callee", "_this", "_this$beforeHooksAtta", "_this$afterHooksAttac", "_this$attachmentSteps2", "threadId", "thread", "_test$parent$titlePat", "_test$parent$titlePat2", "projectSuiteTitle", "fileSuiteTitle", "beforeHooksStack", "afterHooksStack", "attachmentsInBeforeHooks", "attachmentsInAfterHooks", "attachmentToStepMap", "attachmentIndex", "_iterator2", "_step2", "_hookStep", "_iterator3", "_step3", "stepUuid", "_iterator4", "_step4", "_hookStep2", "attachment", "stepInfo", "_i", "_attachment", "_stepInfo", "hookStep", "isBeforeHook", "targetStack", "fileName", "_context", "parallelIndex", "workerIndex", "process", "pid", "updateTest", "testResult", "getHostLabel", "getThreadLabel", "<PERSON><PERSON><PERSON><PERSON>", "PARENT_SUITE", "SUITE", "SUB_SUITE", "_test$annotations", "skipR<PERSON>on", "find", "message", "statusToAllureStats", "expectedStatus", "processAttachment", "stdout", "writeAttachment", "<PERSON><PERSON><PERSON>", "stripAnsi", "contentType", "ContentType", "TEXT", "stderr", "ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE", "findStepByUuid", "addAttachment", "source", "mappedLabels", "reduce", "acc", "label", "<PERSON><PERSON><PERSON><PERSON>", "flatMap", "labelName", "labelsGroup", "_testResult$steps", "unshift", "_testResult$steps2", "stopTest", "writeTest", "onTestEnd", "_x", "_x2", "_addSkippedResults", "_callee2", "_this2", "unprocessedCases", "_iterator5", "_step5", "testCase", "_t", "_context2", "allTests", "_ref4", "SKIPPED", "errors", "retry", "globalStartTime", "addSkippedResults", "_onEnd", "_callee3", "_context3", "writeEnvironmentInfo", "writeCategoriesDefinitions", "onEnd", "printsToStdio", "processStepMetadataMessage", "attachmentStepUuid", "_message$data", "data", "_message$data$paramet", "_step$parameters", "_processAttachment", "_callee4", "allureRuntimeMessage", "parentUuid", "pathWithoutEnd", "actualBase64", "expectedBase64", "diffBase64", "diffName", "_context4", "body", "JSON", "parse", "applyRuntimeMessages", "existsSync", "match", "diffEndRegexp", "processedDiffs", "readImageAsBase64", "stringify", "expected", "actual", "diff", "IMAGEDIFF", "fileExtension", "_x3", "_x4", "_x5", "version", "isDescendantOfStepWithTitle", "allure", "allurePlaywrightLegacyApi", "_default"], "sources": ["../../src/index.ts"], "sourcesContent": ["/* eslint max-lines: off */\nimport type { FullConfig } from \"@playwright/test\";\nimport type { TestResult as PlaywrightTestResult, Suite, TestCase, TestStep } from \"@playwright/test/reporter\";\nimport { existsSync } from \"node:fs\";\nimport path from \"node:path\";\nimport process from \"node:process\";\nimport {\n  ContentType,\n  type ImageDiffAttachment,\n  type Label,\n  LabelName,\n  LinkType,\n  Stage,\n  Status,\n  type StepResult,\n  type TestResult,\n} from \"allure-js-commons\";\nimport type { RuntimeMessage, RuntimeStepMetadataMessage, TestPlanV1Test } from \"allure-js-commons/sdk\";\nimport {\n  extractMetadataFromString,\n  getMessageAndTraceFromError,\n  getMetadataLabel,\n  hasLabel,\n  stripAnsi,\n} from \"allure-js-commons/sdk\";\nimport {\n  ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE,\n  ReporterRuntime,\n  ShallowStepsStack,\n  createDefaultWriter,\n  createStepResult,\n  escapeRegExp,\n  formatLink,\n  getEnvironmentLabels,\n  getFrameworkLabel,\n  getHostLabel,\n  getLanguageLabel,\n  getPackageLabel,\n  getThreadLabel,\n  getWorstTestStepResult,\n  md5,\n  parseTestPlan,\n  randomUuid,\n  readImageAsBase64,\n} from \"allure-js-commons/sdk/reporter\";\nimport { allurePlaywrightLegacyApi } from \"./legacy.js\";\nimport type { AllurePlaywrightReporterConfig, AttachStack, ReporterV2 } from \"./model.js\";\nimport {\n  AFTER_HOOKS_ROOT_STEP_TITLE,\n  BEFORE_HOOKS_ROOT_STEP_TITLE,\n  diffEndRegexp,\n  isAfterHookStep,\n  isBeforeHookStep,\n  isDescendantOfStepWithTitle,\n  normalizeHookTitle,\n  statusToAllureStats,\n} from \"./utils.js\";\n\nexport class AllureReporter implements ReporterV2 {\n  config!: FullConfig;\n  suite!: Suite;\n  options: AllurePlaywrightReporterConfig;\n\n  private allureRuntime: ReporterRuntime | undefined;\n  private globalStartTime = new Date();\n  private processedDiffs: string[] = [];\n  private readonly startedTestCasesTitlesCache: string[] = [];\n  private readonly allureResultsUuids: Map<string, string> = new Map();\n  private readonly attachmentSteps: Map<string, (string | undefined)[]> = new Map();\n  private beforeHooksStepsStack: Map<string, ShallowStepsStack> = new Map();\n  private afterHooksStepsStack: Map<string, ShallowStepsStack> = new Map();\n  private beforeHooksAttachmentsStack: Map<string, AttachStack[]> = new Map();\n  private afterHooksAttachmentsStack: Map<string, AttachStack[]> = new Map();\n\n  constructor(config: AllurePlaywrightReporterConfig) {\n    this.options = { suiteTitle: true, detail: true, ...config };\n  }\n\n  onConfigure(config: FullConfig): void {\n    this.config = config;\n\n    const testPlan = parseTestPlan();\n\n    if (!testPlan) {\n      return;\n    }\n\n    // @ts-ignore\n    const configElement = config[Object.getOwnPropertySymbols(config)[0]];\n\n    if (!configElement) {\n      return;\n    }\n\n    const testsWithSelectors = testPlan.tests.filter((test) => test.selector);\n    const v1ReporterTests: TestPlanV1Test[] = [];\n    const v2ReporterTests: TestPlanV1Test[] = [];\n    const cliArgs: string[] = [];\n\n    testsWithSelectors.forEach((test) => {\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      if (!/#/.test(test.selector!)) {\n        v2ReporterTests.push(test);\n        return;\n      }\n\n      v1ReporterTests.push(test);\n    });\n\n    // The path needs to be specific to the current OS. Otherwise, it may not match against the test file.\n    const selectorToGrepPattern = (selector: string) => escapeRegExp(path.normalize(`/${selector}`));\n\n    if (v2ReporterTests.length) {\n      // we need to cut off column because playwright works only with line number\n      const v2SelectorsArgs = v2ReporterTests\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        .map((test) => test.selector!.replace(/:\\d+$/, \"\"))\n        .map(selectorToGrepPattern);\n\n      cliArgs.push(...v2SelectorsArgs);\n    }\n\n    if (v1ReporterTests.length) {\n      const v1SelectorsArgs = v1ReporterTests\n        // we can filter tests only by absolute path, so we need to cut off test name\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        .map((test) => test.selector!.split(\"#\")[0])\n        .map(selectorToGrepPattern);\n\n      cliArgs.push(...v1SelectorsArgs);\n    }\n\n    if (!cliArgs.length) {\n      return;\n    }\n\n    configElement.cliArgs = cliArgs;\n  }\n\n  onError(): void {}\n\n  onExit(): void {}\n\n  onStdErr(): void {}\n\n  onStdOut(): void {}\n\n  onBegin(suite: Suite): void {\n    this.suite = suite;\n    this.allureRuntime = new ReporterRuntime({\n      ...this.options,\n      writer: createDefaultWriter({ resultsDir: this.options.resultsDir }),\n    });\n  }\n\n  onTestBegin(test: TestCase) {\n    const suite = test.parent;\n    const titleMetadata = extractMetadataFromString(test.title);\n    const project = suite.project()!;\n    const testFilePath = path.relative(project?.testDir, test.location.file);\n    const relativeFileParts = testFilePath.split(path.sep);\n    const relativeFile = relativeFileParts.join(\"/\");\n    // root > project > file path > test.describe...\n    const [, , , ...suiteTitles] = suite.titlePath();\n    const nameSuites = suiteTitles.length > 0 ? `${suiteTitles.join(\" \")} ` : \"\";\n    const testCaseIdBase = `${relativeFile}#${nameSuites}${test.title}`;\n    const result: Partial<TestResult> = {\n      name: titleMetadata.cleanTitle,\n      labels: [...titleMetadata.labels, ...getEnvironmentLabels()],\n      links: [...titleMetadata.links],\n      parameters: [],\n      steps: [],\n      testCaseId: md5(testCaseIdBase),\n      fullName: `${relativeFile}:${test.location.line}:${test.location.column}`,\n      titlePath: relativeFileParts.concat(...suiteTitles),\n    };\n\n    result.labels!.push(getLanguageLabel());\n    result.labels!.push(getFrameworkLabel(\"playwright\"));\n    result.labels!.push(getPackageLabel(testFilePath));\n    result.labels!.push({ name: \"titlePath\", value: suite.titlePath().join(\" > \") });\n\n    // support for earlier playwright versions\n    if (\"tags\" in test) {\n      const tags: Label[] = test.tags.map((tag) => ({\n        name: LabelName.TAG,\n        value: tag.startsWith(\"@\") ? tag.substring(1) : tag,\n      }));\n      result.labels!.push(...tags);\n    }\n\n    if (\"annotations\" in test) {\n      for (const annotation of test.annotations) {\n        if (annotation.type === \"skip\" || annotation.type === \"fixme\") {\n          continue;\n        }\n\n        if (annotation.type === \"issue\") {\n          result.links!.push(\n            formatLink(this.options.links ?? {}, {\n              type: LinkType.ISSUE,\n              url: annotation.description!,\n            }),\n          );\n          continue;\n        }\n\n        if (annotation.type === \"tms\" || annotation.type === \"test_key\") {\n          result.links!.push(\n            formatLink(this.options.links ?? {}, {\n              type: LinkType.TMS,\n              url: annotation.description!,\n            }),\n          );\n          continue;\n        }\n\n        if (annotation.type === \"description\") {\n          result.description = annotation.description;\n          continue;\n        }\n\n        const annotationLabel = getMetadataLabel(annotation.type, annotation.description);\n\n        if (annotationLabel) {\n          result.labels!.push(annotationLabel);\n          continue;\n        }\n\n        result.steps!.push({\n          name: `${annotation.type}: ${annotation.description!}`,\n          status: Status.PASSED,\n          stage: Stage.FINISHED,\n          parameters: [],\n          steps: [],\n          attachments: [],\n          statusDetails: {},\n        });\n      }\n    }\n\n    if (project?.name) {\n      result.parameters!.push({ name: \"Project\", value: project.name });\n    }\n\n    if (project?.repeatEach > 1) {\n      result.parameters!.push({ name: \"Repetition\", value: `${test.repeatEachIndex + 1}` });\n    }\n\n    const testUuid = this.allureRuntime!.startTest(result);\n\n    this.allureResultsUuids.set(test.id, testUuid);\n    this.startedTestCasesTitlesCache.push(titleMetadata.cleanTitle);\n  }\n\n  #shouldIgnoreStep(step: TestStep) {\n    if (!this.options.detail && step.category !== \"test.step\") {\n      return true;\n    }\n\n    // ignore noisy route.continue()\n    if (step.category === \"pw:api\" && step.title === \"route.continue()\") {\n      return true;\n    }\n\n    // playwright doesn't report this step\n    if (step.title === \"Worker Cleanup\" || isDescendantOfStepWithTitle(step, \"Worker Cleanup\")) {\n      return true;\n    }\n\n    return false;\n  }\n\n  onStepBegin(test: TestCase, _result: PlaywrightTestResult, step: TestStep): void {\n    const isRootBeforeHook = step.title === BEFORE_HOOKS_ROOT_STEP_TITLE;\n    const isRootAfterHook = step.title === AFTER_HOOKS_ROOT_STEP_TITLE;\n    const isRootHook = isRootBeforeHook || isRootAfterHook;\n    const isBeforeHookDescendant = isBeforeHookStep(step);\n    const isAfterHookDescendant = isAfterHookStep(step);\n    const isHookStep = isBeforeHookDescendant || isAfterHookDescendant;\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n\n    if ([\"test.attach\", \"attach\"].includes(step.category) && !isHookStep) {\n      const currentStep = this.allureRuntime?.currentStep(testUuid);\n      this.attachmentSteps.set(testUuid, [...(this.attachmentSteps.get(testUuid) ?? []), currentStep]);\n      return;\n    }\n\n    if (this.#shouldIgnoreStep(step)) {\n      return;\n    }\n\n    const baseStep: StepResult = {\n      ...createStepResult(),\n      name: step.title,\n      start: step.startTime.getTime(),\n      stage: Stage.RUNNING,\n      uuid: randomUuid(),\n    };\n\n    if (isHookStep) {\n      const stack = isBeforeHookDescendant\n        ? this.beforeHooksStepsStack.get(test.id)!\n        : this.afterHooksStepsStack.get(test.id)!;\n\n      if ([\"test.attach\", \"attach\"].includes(step.category)) {\n        stack.startStep(baseStep);\n\n        const attachStack = isBeforeHookDescendant ? this.beforeHooksAttachmentsStack : this.afterHooksAttachmentsStack;\n\n        stack.updateStep((stepResult) => {\n          stepResult.name = normalizeHookTitle(stepResult.name!);\n          stepResult.stage = Stage.FINISHED;\n          attachStack.set(test.id, [...(attachStack.get(test.id) ?? []), { ...step, uuid: stepResult.uuid as string }]);\n        });\n        stack.stopStep();\n        return;\n      }\n      stack.startStep(baseStep);\n\n      return;\n    }\n\n    if (isRootHook) {\n      const stack = new ShallowStepsStack();\n      stack.startStep(baseStep);\n      if (isRootBeforeHook) {\n        this.beforeHooksStepsStack.set(test.id, stack);\n      } else {\n        this.afterHooksStepsStack.set(test.id, stack);\n      }\n      return;\n    }\n\n    this.allureRuntime!.startStep(testUuid, undefined, baseStep)!;\n  }\n\n  onStepEnd(test: TestCase, _result: PlaywrightTestResult, step: TestStep): void {\n    if (this.#shouldIgnoreStep(step)) {\n      return;\n    }\n    // ignore test.attach steps since attachments are already in the report\n    if ([\"test.attach\", \"attach\"].includes(step.category)) {\n      return;\n    }\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n    const isRootBeforeHook = step.title === BEFORE_HOOKS_ROOT_STEP_TITLE;\n    const isRootAfterHook = step.title === AFTER_HOOKS_ROOT_STEP_TITLE;\n    const isBeforeHookDescendant = isBeforeHookStep(step);\n    const isAfterHookDescendant = isAfterHookStep(step);\n    const isAfterHook = isRootAfterHook || isAfterHookDescendant;\n    const isHook = isRootBeforeHook || isRootAfterHook || isBeforeHookDescendant || isAfterHookDescendant;\n\n    if (isHook) {\n      const stack = isAfterHook ? this.afterHooksStepsStack.get(test.id)! : this.beforeHooksStepsStack.get(test.id)!;\n\n      stack.updateStep((stepResult) => {\n        const { status = Status.PASSED } = getWorstTestStepResult(stepResult.steps) ?? {};\n        stepResult.status = step.error ? Status.FAILED : status;\n        stepResult.stage = Stage.FINISHED;\n        if (step.error) {\n          stepResult.statusDetails = { ...getMessageAndTraceFromError(step.error) };\n        }\n      });\n      stack.stopStep({\n        duration: step.duration,\n      });\n      return;\n    }\n\n    const currentStep = this.allureRuntime!.currentStep(testUuid);\n\n    if (!currentStep) {\n      return;\n    }\n\n    this.allureRuntime!.updateStep(currentStep, (stepResult) => {\n      const { status = Status.PASSED } = getWorstTestStepResult(stepResult.steps) ?? {};\n      stepResult.status = step.error ? Status.FAILED : status;\n      stepResult.stage = Stage.FINISHED;\n      if (step.error) {\n        stepResult.statusDetails = { ...getMessageAndTraceFromError(step.error) };\n      }\n    });\n    this.allureRuntime!.stopStep(currentStep, { duration: step.duration });\n  }\n\n  async onTestEnd(test: TestCase, result: PlaywrightTestResult) {\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n    // We need to check parallelIndex first because pw introduced this field only in v1.30.0\n    const threadId = result.parallelIndex !== undefined ? result.parallelIndex : result.workerIndex;\n    const thread = `pid-${process.pid}-worker-${threadId}`;\n    const error = result.error;\n    // only apply default suites if not set by user\n    const [, projectSuiteTitle, fileSuiteTitle, ...suiteTitles] = test.parent.titlePath();\n    const beforeHooksStack = this.beforeHooksStepsStack.get(test.id);\n    const afterHooksStack = this.afterHooksStepsStack.get(test.id);\n\n    this.allureRuntime!.updateTest(testUuid, (testResult) => {\n      testResult.labels.push(getHostLabel());\n      testResult.labels.push(getThreadLabel(thread));\n\n      if (projectSuiteTitle && !hasLabel(testResult, LabelName.PARENT_SUITE)) {\n        testResult.labels.push({ name: LabelName.PARENT_SUITE, value: projectSuiteTitle });\n      }\n\n      if (this.options.suiteTitle && fileSuiteTitle && !hasLabel(testResult, LabelName.SUITE)) {\n        testResult.labels.push({ name: LabelName.SUITE, value: fileSuiteTitle });\n      }\n\n      if (suiteTitles.length > 0 && !hasLabel(testResult, LabelName.SUB_SUITE)) {\n        testResult.labels.push({ name: LabelName.SUB_SUITE, value: suiteTitles.join(\" > \") });\n      }\n\n      if (error) {\n        testResult.statusDetails = { ...getMessageAndTraceFromError(error) };\n      } else {\n        const skipReason = test.annotations?.find(\n          (annotation) => annotation.type === \"skip\" || annotation.type === \"fixme\",\n        )?.description;\n\n        if (skipReason) {\n          testResult.statusDetails = { ...testResult.statusDetails, message: skipReason };\n        }\n      }\n\n      testResult.status = statusToAllureStats(result.status, test.expectedStatus);\n      testResult.stage = Stage.FINISHED;\n    });\n\n    const attachmentsInBeforeHooks = this.beforeHooksAttachmentsStack.get(test.id) ?? [];\n    const attachmentsInAfterHooks = this.afterHooksAttachmentsStack.get(test.id) ?? [];\n    const attachmentSteps = this.attachmentSteps.get(testUuid) ?? [];\n\n    const attachmentToStepMap = new Map<number, { stepUuid?: string; isHook: boolean; hookStep?: AttachStack }>();\n\n    let attachmentIndex = 0;\n\n    for (const hookStep of attachmentsInBeforeHooks) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid: hookStep.uuid,\n        isHook: true,\n        hookStep,\n      });\n      attachmentIndex++;\n    }\n\n    for (const stepUuid of attachmentSteps) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid,\n        isHook: false,\n      });\n      attachmentIndex++;\n    }\n\n    for (const hookStep of attachmentsInAfterHooks) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid: hookStep.uuid,\n        isHook: true,\n        hookStep,\n      });\n      attachmentIndex++;\n    }\n\n    for (let i = 0; i < result.attachments.length; i++) {\n      const attachment = result.attachments[i];\n      const stepInfo = attachmentToStepMap.get(i);\n\n      if (stepInfo?.isHook) {\n        continue;\n      } else if (stepInfo?.stepUuid) {\n        await this.processAttachment(testUuid, stepInfo.stepUuid, attachment);\n      } else {\n        await this.processAttachment(testUuid, undefined, attachment);\n      }\n    }\n\n    if (result.stdout.length > 0) {\n      this.allureRuntime!.writeAttachment(\n        testUuid,\n        undefined,\n        \"stdout\",\n        Buffer.from(stripAnsi(result.stdout.join(\"\")), \"utf-8\"),\n        {\n          contentType: ContentType.TEXT,\n        },\n      );\n    }\n\n    if (result.stderr.length > 0) {\n      this.allureRuntime!.writeAttachment(\n        testUuid,\n        undefined,\n        \"stderr\",\n        Buffer.from(stripAnsi(result.stderr.join(\"\")), \"utf-8\"),\n        {\n          contentType: ContentType.TEXT,\n        },\n      );\n    }\n\n    // FIXME: temp logic for labels override, we need it here to keep the reporter compatible with v2 API\n    // in next iterations we need to implement the logic for every javascript integration\n\n    for (let i = 0; i < result.attachments.length; i++) {\n      const attachment = result.attachments[i];\n      const stepInfo = attachmentToStepMap.get(i);\n\n      if (stepInfo?.isHook && stepInfo.hookStep) {\n        const hookStep = stepInfo.hookStep;\n        const isBeforeHook = attachmentsInBeforeHooks.includes(hookStep);\n        const targetStack = isBeforeHook ? beforeHooksStack : afterHooksStack;\n\n        if (attachment.contentType === ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE) {\n          await this.processAttachment(testUuid, hookStep.uuid, attachment);\n          continue;\n        }\n\n        if (targetStack) {\n          const stepResult = targetStack.findStepByUuid(hookStep.uuid);\n          if (stepResult) {\n            const fileName = targetStack.addAttachment(attachment, this.allureRuntime!.writer);\n            stepResult.attachments.push({\n              name: attachment.name,\n              type: attachment.contentType,\n              source: fileName,\n            });\n          }\n        }\n      }\n    }\n\n    this.allureRuntime!.updateTest(testUuid, (testResult) => {\n      const mappedLabels = testResult.labels.reduce<Record<string, Label[]>>((acc, label) => {\n        if (!acc[label.name]) {\n          acc[label.name] = [];\n        }\n\n        acc[label.name].push(label);\n\n        return acc;\n      }, {});\n      const newLabels = Object.keys(mappedLabels).flatMap((labelName) => {\n        const labelsGroup = mappedLabels[labelName];\n\n        if (\n          labelName === LabelName.SUITE ||\n          labelName === LabelName.PARENT_SUITE ||\n          labelName === LabelName.SUB_SUITE\n        ) {\n          return labelsGroup.slice(-1);\n        }\n\n        return labelsGroup;\n      });\n\n      if (beforeHooksStack) {\n        testResult.steps.unshift(...beforeHooksStack.steps);\n        this.beforeHooksStepsStack.delete(test.id);\n      }\n\n      if (afterHooksStack) {\n        testResult.steps.push(...afterHooksStack.steps);\n        this.afterHooksStepsStack.delete(test.id);\n      }\n\n      testResult.labels = newLabels;\n    });\n    this.allureRuntime!.stopTest(testUuid, { duration: result.duration });\n    this.allureRuntime!.writeTest(testUuid);\n  }\n\n  async addSkippedResults() {\n    const unprocessedCases = this.suite.allTests().filter(({ title }) => {\n      const titleMetadata = extractMetadataFromString(title);\n\n      return !this.startedTestCasesTitlesCache.includes(titleMetadata.cleanTitle);\n    });\n\n    for (const testCase of unprocessedCases) {\n      this.onTestBegin(testCase);\n      await this.onTestEnd(testCase, {\n        status: Status.SKIPPED,\n        attachments: [],\n        duration: 0,\n        errors: [],\n        parallelIndex: 0,\n        workerIndex: 0,\n        retry: 0,\n        steps: [],\n        stderr: [],\n        stdout: [],\n        startTime: this.globalStartTime,\n        annotations: [],\n      });\n    }\n  }\n\n  async onEnd() {\n    await this.addSkippedResults();\n\n    this.allureRuntime!.writeEnvironmentInfo();\n    this.allureRuntime!.writeCategoriesDefinitions();\n  }\n\n  printsToStdio(): boolean {\n    return false;\n  }\n\n  private processStepMetadataMessage(attachmentStepUuid: string, message: RuntimeStepMetadataMessage) {\n    const { name, parameters = [] } = message.data;\n\n    this.allureRuntime!.updateStep(attachmentStepUuid, (step) => {\n      if (name) {\n        step.name = name;\n      }\n\n      step.parameters.push(...parameters);\n    });\n  }\n\n  private async processAttachment(\n    testUuid: string,\n    attachmentStepUuid: string | undefined,\n    attachment: {\n      name: string;\n      contentType: string;\n      path?: string;\n      body?: Buffer;\n    },\n  ) {\n    if (!attachment.body && !attachment.path) {\n      return;\n    }\n\n    const allureRuntimeMessage = attachment.contentType === ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE;\n\n    if (allureRuntimeMessage && !attachment.body) {\n      return;\n    }\n\n    if (allureRuntimeMessage) {\n      const message = JSON.parse(attachment.body!.toString()) as RuntimeMessage;\n\n      if (message.type === \"step_metadata\") {\n        this.processStepMetadataMessage(attachmentStepUuid!, message);\n        return;\n      }\n\n      this.allureRuntime!.applyRuntimeMessages(testUuid, [message]);\n      return;\n    }\n\n    const parentUuid = this.allureRuntime!.startStep(testUuid, attachmentStepUuid, { name: attachment.name });\n\n    // only stop if step is created. Step may not be created only if test with specified uuid doesn't exists.\n    // usually, missing test by uuid means we should completely skip result processing;\n    // the later operations are safe and will only produce console warnings\n    if (parentUuid) {\n      this.allureRuntime!.stopStep(parentUuid, undefined);\n    }\n\n    if (attachment.body) {\n      this.allureRuntime!.writeAttachment(testUuid, parentUuid, attachment.name, attachment.body, {\n        contentType: attachment.contentType,\n      });\n    } else if (!existsSync(attachment.path!)) {\n      return;\n    } else {\n      const contentType =\n        attachment.name === \"trace\" && attachment.contentType === \"application/zip\"\n          ? \"application/vnd.allure.playwright-trace\"\n          : attachment.contentType;\n\n      this.allureRuntime!.writeAttachment(testUuid, parentUuid, attachment.name, attachment.path!, {\n        contentType,\n      });\n    }\n\n    if (!attachment.name.match(diffEndRegexp)) {\n      return;\n    }\n\n    const pathWithoutEnd = attachment.path!.replace(diffEndRegexp, \"\");\n\n    if (this.processedDiffs.includes(pathWithoutEnd)) {\n      return;\n    }\n\n    const actualBase64 = await readImageAsBase64(`${pathWithoutEnd}-actual.png`);\n    const expectedBase64 = await readImageAsBase64(`${pathWithoutEnd}-expected.png`);\n    const diffBase64 = await readImageAsBase64(`${pathWithoutEnd}-diff.png`);\n    const diffName = attachment.name.replace(diffEndRegexp, \"\");\n\n    this.allureRuntime!.writeAttachment(\n      testUuid,\n      undefined,\n      diffName,\n      Buffer.from(\n        JSON.stringify({\n          expected: expectedBase64,\n          actual: actualBase64,\n          diff: diffBase64,\n          name: diffName,\n        } as ImageDiffAttachment),\n        \"utf-8\",\n      ),\n      {\n        contentType: ContentType.IMAGEDIFF,\n        fileExtension: \".imagediff\",\n      },\n    );\n\n    this.processedDiffs.push(pathWithoutEnd);\n  }\n\n  version(): \"v2\" {\n    return \"v2\";\n  }\n}\n\n/**\n * @deprecated for removal, import functions directly from \"allure-js-commons\".\n */\nexport const allure = allurePlaywrightLegacyApi;\n\n/**\n * @deprecated for removal, import functions directly from \"@playwright/test\".\n */\nexport { test, expect } from \"@playwright/test\";\n\nexport default AllureReporter;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AAYA,IAAAK,IAAA,GAAAL,OAAA;AAOA,IAAAM,SAAA,GAAAN,OAAA;AAoBA,IAAAO,OAAA,GAAAP,OAAA;AAEA,IAAAQ,MAAA,GAAAR,OAAA;AA0qBA,IAAAS,KAAA,GAAAT,OAAA;AAAgD,SAAAE,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,aAAA,IAxtBhD,uKAAAF,CAAA,EAAAG,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAAtB,CAAA,EAAAuB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAzB,CAAA,MAAAwB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAd,CAAA,EAAAqB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAV,CAAA,IAAAU,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAX,CAAA,GAAAc,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,eAAAP,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAV,CAAA,cAAAG,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAV,CAAA,EAAAW,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAmB,kBAAA,cAAAC,2BAAA,KAAA9B,CAAA,GAAAY,MAAA,CAAAmB,cAAA,MAAAvB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAmB,0BAAA,CAAArB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAlB,CAAA,WAAAe,MAAA,CAAAoB,cAAA,GAAApB,MAAA,CAAAoB,cAAA,CAAAnC,CAAA,EAAAiC,0BAAA,KAAAjC,CAAA,CAAAoC,SAAA,GAAAH,0BAAA,EAAAhB,mBAAA,CAAAjB,CAAA,EAAAQ,CAAA,yBAAAR,CAAA,CAAAY,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAd,CAAA,WAAAgC,iBAAA,CAAApB,SAAA,GAAAqB,0BAAA,EAAAhB,mBAAA,CAAAH,CAAA,iBAAAmB,0BAAA,GAAAhB,mBAAA,CAAAgB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAApB,mBAAA,CAAAgB,0BAAA,EAAAzB,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAZ,YAAA,YAAAA,aAAA,aAAAoC,CAAA,EAAA5B,CAAA,EAAA6B,CAAA,EAAArB,CAAA;AAAA,SAAAD,oBAAAjB,CAAA,EAAAI,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAAyB,cAAA,QAAA9B,CAAA,uBAAAV,CAAA,IAAAU,CAAA,QAAAO,mBAAA,YAAAwB,mBAAAzC,CAAA,EAAAI,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAM,CAAA,GAAAA,CAAA,CAAAV,CAAA,EAAAI,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAoC,UAAA,GAAAvC,CAAA,EAAAwC,YAAA,GAAAxC,CAAA,EAAAyC,QAAA,GAAAzC,CAAA,MAAAH,CAAA,CAAAI,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAjB,CAAA,EAAAI,CAAA,YAAAJ,CAAA,gBAAA6C,OAAA,CAAAzC,CAAA,EAAAE,CAAA,EAAAN,CAAA,UAAAQ,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAS,mBAAA,CAAAjB,CAAA,EAAAI,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA2C,mBAAAxC,CAAA,EAAAH,CAAA,EAAAH,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAN,CAAA,CAAAM,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAiC,OAAA,CAAAC,OAAA,CAAAlC,CAAA,EAAAmC,IAAA,CAAA7C,CAAA,EAAAI,CAAA;AAAA,SAAA0C,kBAAA5C,CAAA,6BAAAH,CAAA,SAAAH,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA3C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAA8C,KAAA,CAAAjD,CAAA,EAAAH,CAAA,YAAAqD,MAAA/C,CAAA,IAAAwC,kBAAA,CAAAvB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA6C,KAAA,EAAAC,MAAA,UAAAhD,CAAA,cAAAgD,OAAAhD,CAAA,IAAAwC,kBAAA,CAAAvB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA6C,KAAA,EAAAC,MAAA,WAAAhD,CAAA,KAAA+C,KAAA;AAAA,SAAAE,2BAAAnD,CAAA,EAAAJ,CAAA,QAAAG,CAAA,yBAAAE,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,KAAAH,CAAA,qBAAAD,CAAA,QAAAqD,KAAA,CAAAC,OAAA,CAAArD,CAAA,MAAAD,CAAA,GAAAuD,2BAAA,CAAAtD,CAAA,MAAAJ,CAAA,IAAAI,CAAA,uBAAAA,CAAA,CAAAsB,MAAA,IAAAvB,CAAA,KAAAC,CAAA,GAAAD,CAAA,OAAAwD,EAAA,MAAAC,CAAA,YAAAA,EAAA,eAAAC,CAAA,EAAAD,CAAA,EAAAtD,CAAA,WAAAA,EAAA,WAAAqD,EAAA,IAAAvD,CAAA,CAAAsB,MAAA,KAAAI,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAA3B,CAAA,CAAAuD,EAAA,UAAA3D,CAAA,WAAAA,EAAAI,CAAA,UAAAA,CAAA,KAAAc,CAAA,EAAA0C,CAAA,gBAAAhC,SAAA,iJAAApB,CAAA,EAAAe,CAAA,OAAAT,CAAA,gBAAA+C,CAAA,WAAAA,EAAA,IAAA1D,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAzB,CAAA,MAAAE,CAAA,WAAAA,EAAA,QAAAF,CAAA,GAAAD,CAAA,CAAA2D,IAAA,WAAAvC,CAAA,GAAAnB,CAAA,CAAA0B,IAAA,EAAA1B,CAAA,KAAAJ,CAAA,WAAAA,EAAAI,CAAA,IAAAU,CAAA,OAAAN,CAAA,GAAAJ,CAAA,KAAAc,CAAA,WAAAA,EAAA,UAAAK,CAAA,YAAApB,CAAA,cAAAA,CAAA,8BAAAW,CAAA,QAAAN,CAAA;AAAA,SAAAuD,SAAA3D,CAAA,WAAA4D,eAAA,CAAA5D,CAAA,KAAA6D,gBAAA,CAAA7D,CAAA,KAAAsD,2BAAA,CAAAtD,CAAA,KAAA8D,gBAAA;AAAA,SAAAA,iBAAA,cAAAtC,SAAA;AAAA,SAAAoC,gBAAA5D,CAAA,QAAAoD,KAAA,CAAAC,OAAA,CAAArD,CAAA,UAAAA,CAAA;AAAA,SAAA+D,mBAAA/D,CAAA,WAAAgE,kBAAA,CAAAhE,CAAA,KAAA6D,gBAAA,CAAA7D,CAAA,KAAAsD,2BAAA,CAAAtD,CAAA,KAAAiE,kBAAA;AAAA,SAAAA,mBAAA,cAAAzC,SAAA;AAAA,SAAA8B,4BAAAtD,CAAA,EAAAmB,CAAA,QAAAnB,CAAA,2BAAAA,CAAA,SAAAkE,iBAAA,CAAAlE,CAAA,EAAAmB,CAAA,OAAApB,CAAA,MAAAoE,QAAA,CAAA1C,IAAA,CAAAzB,CAAA,EAAAoE,KAAA,6BAAArE,CAAA,IAAAC,CAAA,CAAAqE,WAAA,KAAAtE,CAAA,GAAAC,CAAA,CAAAqE,WAAA,CAAAC,IAAA,aAAAvE,CAAA,cAAAA,CAAA,GAAAqD,KAAA,CAAAmB,IAAA,CAAAvE,CAAA,oBAAAD,CAAA,+CAAAyE,IAAA,CAAAzE,CAAA,IAAAmE,iBAAA,CAAAlE,CAAA,EAAAmB,CAAA;AAAA,SAAA0C,iBAAA7D,CAAA,8BAAAC,MAAA,YAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,aAAAH,CAAA,uBAAAoD,KAAA,CAAAmB,IAAA,CAAAvE,CAAA;AAAA,SAAAgE,mBAAAhE,CAAA,QAAAoD,KAAA,CAAAC,OAAA,CAAArD,CAAA,UAAAkE,iBAAA,CAAAlE,CAAA;AAAA,SAAAkE,kBAAAlE,CAAA,EAAAmB,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,MAAAH,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,YAAA1B,CAAA,MAAAM,CAAA,GAAAkD,KAAA,CAAAjC,CAAA,GAAAvB,CAAA,GAAAuB,CAAA,EAAAvB,CAAA,IAAAM,CAAA,CAAAN,CAAA,IAAAI,CAAA,CAAAJ,CAAA,UAAAM,CAAA;AAAA,SAAAuE,QAAA7E,CAAA,EAAAI,CAAA,QAAAD,CAAA,GAAAY,MAAA,CAAA+D,IAAA,CAAA9E,CAAA,OAAAe,MAAA,CAAAgE,qBAAA,QAAAvE,CAAA,GAAAO,MAAA,CAAAgE,qBAAA,CAAA/E,CAAA,GAAAI,CAAA,KAAAI,CAAA,GAAAA,CAAA,CAAAwE,MAAA,WAAA5E,CAAA,WAAAW,MAAA,CAAAkE,wBAAA,CAAAjF,CAAA,EAAAI,CAAA,EAAAsC,UAAA,OAAAvC,CAAA,CAAA+E,IAAA,CAAA9B,KAAA,CAAAjD,CAAA,EAAAK,CAAA,YAAAL,CAAA;AAAA,SAAAgF,cAAAnF,CAAA,aAAAI,CAAA,MAAAA,CAAA,GAAA+C,SAAA,CAAAzB,MAAA,EAAAtB,CAAA,UAAAD,CAAA,WAAAgD,SAAA,CAAA/C,CAAA,IAAA+C,SAAA,CAAA/C,CAAA,QAAAA,CAAA,OAAAyE,OAAA,CAAA9D,MAAA,CAAAZ,CAAA,OAAAiF,OAAA,WAAAhF,CAAA,IAAAiF,eAAA,CAAArF,CAAA,EAAAI,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAW,MAAA,CAAAuE,yBAAA,GAAAvE,MAAA,CAAAwE,gBAAA,CAAAvF,CAAA,EAAAe,MAAA,CAAAuE,yBAAA,CAAAnF,CAAA,KAAA0E,OAAA,CAAA9D,MAAA,CAAAZ,CAAA,GAAAiF,OAAA,WAAAhF,CAAA,IAAAW,MAAA,CAAAyB,cAAA,CAAAxC,CAAA,EAAAI,CAAA,EAAAW,MAAA,CAAAkE,wBAAA,CAAA9E,CAAA,EAAAC,CAAA,iBAAAJ,CAAA;AAAA,SAAAwF,gBAAAjE,CAAA,EAAAjB,CAAA,UAAAiB,CAAA,YAAAjB,CAAA,aAAAsB,SAAA;AAAA,SAAA6D,kBAAAzF,CAAA,EAAAI,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAsB,MAAA,EAAAvB,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAAkC,UAAA,GAAAlC,CAAA,CAAAkC,UAAA,QAAAlC,CAAA,CAAAmC,YAAA,kBAAAnC,CAAA,KAAAA,CAAA,CAAAoC,QAAA,QAAA7B,MAAA,CAAAyB,cAAA,CAAAxC,CAAA,EAAA0F,cAAA,CAAAlF,CAAA,CAAAmF,GAAA,GAAAnF,CAAA;AAAA,SAAAoF,aAAA5F,CAAA,EAAAI,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAqF,iBAAA,CAAAzF,CAAA,CAAAY,SAAA,EAAAR,CAAA,GAAAD,CAAA,IAAAsF,iBAAA,CAAAzF,CAAA,EAAAG,CAAA,GAAAY,MAAA,CAAAyB,cAAA,CAAAxC,CAAA,iBAAA4C,QAAA,SAAA5C,CAAA;AAAA,SAAA6F,4BAAA7F,CAAA,EAAAuB,CAAA,IAAAuE,0BAAA,CAAA9F,CAAA,EAAAuB,CAAA,GAAAA,CAAA,CAAAwE,GAAA,CAAA/F,CAAA;AAAA,SAAA8F,2BAAA9F,CAAA,EAAAG,CAAA,QAAAA,CAAA,CAAA6F,GAAA,CAAAhG,CAAA,aAAA4B,SAAA;AAAA,SAAAyD,gBAAArF,CAAA,EAAAI,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAsF,cAAA,CAAAtF,CAAA,MAAAJ,CAAA,GAAAe,MAAA,CAAAyB,cAAA,CAAAxC,CAAA,EAAAI,CAAA,IAAA2B,KAAA,EAAA5B,CAAA,EAAAuC,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAA5C,CAAA,CAAAI,CAAA,IAAAD,CAAA,EAAAH,CAAA;AAAA,SAAA0F,eAAAvF,CAAA,QAAAO,CAAA,GAAAuF,YAAA,CAAA9F,CAAA,gCAAA+F,OAAA,CAAAxF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAuF,aAAA9F,CAAA,EAAAC,CAAA,oBAAA8F,OAAA,CAAA/F,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAH,CAAA,GAAAG,CAAA,CAAAE,MAAA,CAAA8F,WAAA,kBAAAnG,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA6B,IAAA,CAAA1B,CAAA,EAAAC,CAAA,gCAAA8F,OAAA,CAAAxF,CAAA,UAAAA,CAAA,YAAAkB,SAAA,yEAAAxB,CAAA,GAAAgG,MAAA,GAAAC,MAAA,EAAAlG,CAAA;AAAA,SAAAmG,kBAAAtG,CAAA,EAAAG,CAAA,EAAAG,CAAA,6BAAAN,CAAA,GAAAA,CAAA,KAAAG,CAAA,GAAAH,CAAA,CAAAgG,GAAA,CAAA7F,CAAA,UAAAgD,SAAA,CAAAzB,MAAA,OAAAvB,CAAA,GAAAG,CAAA,YAAAsB,SAAA,qDADA;AAAA,IAAA2E,qBAAA,oBAAAC,OAAA;AAAA,IA0DaC,cAAc,GAAAC,OAAA,CAAAD,cAAA;EAgBzB,SAAAA,eAAYE,MAAsC,EAAE;IAAAnB,eAAA,OAAAiB,cAAA;IAAAZ,2BAAA,OAAAU,qBAAA;IAAAlB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAV1B,IAAIuB,IAAI,CAAC,CAAC;IAAAvB,eAAA,yBACD,EAAE;IAAAA,eAAA,sCACoB,EAAE;IAAAA,eAAA,6BACA,IAAIwB,GAAG,CAAC,CAAC;IAAAxB,eAAA,0BACI,IAAIwB,GAAG,CAAC,CAAC;IAAAxB,eAAA,gCACjB,IAAIwB,GAAG,CAAC,CAAC;IAAAxB,eAAA,+BACV,IAAIwB,GAAG,CAAC,CAAC;IAAAxB,eAAA,sCACN,IAAIwB,GAAG,CAAC,CAAC;IAAAxB,eAAA,qCACV,IAAIwB,GAAG,CAAC,CAAC;IAGxE,IAAI,CAACC,OAAO,GAAA3B,aAAA;MAAK4B,UAAU,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,GAAKL,MAAM,CAAE;EAC9D;EAAC,OAAAf,YAAA,CAAAa,cAAA;IAAAd,GAAA;IAAA5D,KAAA,EAED,SAAAkF,WAAWA,CAACN,MAAkB,EAAQ;MACpC,IAAI,CAACA,MAAM,GAAGA,MAAM;MAEpB,IAAMO,QAAQ,GAAG,IAAAC,uBAAa,EAAC,CAAC;MAEhC,IAAI,CAACD,QAAQ,EAAE;QACb;MACF;;MAEA;MACA,IAAME,aAAa,GAAGT,MAAM,CAAC5F,MAAM,CAACgE,qBAAqB,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MAErE,IAAI,CAACS,aAAa,EAAE;QAClB;MACF;MAEA,IAAMC,kBAAkB,GAAGH,QAAQ,CAACI,KAAK,CAACtC,MAAM,CAAC,UAACJ,IAAI;QAAA,OAAKA,IAAI,CAAC2C,QAAQ;MAAA,EAAC;MACzE,IAAMC,eAAiC,GAAG,EAAE;MAC5C,IAAMC,eAAiC,GAAG,EAAE;MAC5C,IAAMC,OAAiB,GAAG,EAAE;MAE5BL,kBAAkB,CAACjC,OAAO,CAAC,UAACR,IAAI,EAAK;QACnC;QACA,IAAI,CAAC,GAAG,CAACA,IAAI,CAACA,IAAI,CAAC2C,QAAS,CAAC,EAAE;UAC7BE,eAAe,CAACvC,IAAI,CAACN,IAAI,CAAC;UAC1B;QACF;QAEA4C,eAAe,CAACtC,IAAI,CAACN,IAAI,CAAC;MAC5B,CAAC,CAAC;;MAEF;MACA,IAAM+C,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIJ,QAAgB;QAAA,OAAK,IAAAK,sBAAY,EAACC,oBAAI,CAACC,SAAS,KAAAC,MAAA,CAAKR,QAAQ,CAAE,CAAC,CAAC;MAAA;MAEhG,IAAIE,eAAe,CAAC/F,MAAM,EAAE;QAC1B;QACA,IAAMsG,eAAe,GAAGP;QACtB;QAAA,CACCQ,GAAG,CAAC,UAACrD,IAAI;UAAA,OAAKA,IAAI,CAAC2C,QAAQ,CAAEW,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAAA,EAAC,CAClDD,GAAG,CAACN,qBAAqB,CAAC;QAE7BD,OAAO,CAACxC,IAAI,CAAA9B,KAAA,CAAZsE,OAAO,EAAAvD,kBAAA,CAAS6D,eAAe,EAAC;MAClC;MAEA,IAAIR,eAAe,CAAC9F,MAAM,EAAE;QAC1B,IAAMyG,eAAe,GAAGX;QACtB;QACA;QAAA,CACCS,GAAG,CAAC,UAACrD,IAAI;UAAA,OAAKA,IAAI,CAAC2C,QAAQ,CAAEa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAA,EAAC,CAC3CH,GAAG,CAACN,qBAAqB,CAAC;QAE7BD,OAAO,CAACxC,IAAI,CAAA9B,KAAA,CAAZsE,OAAO,EAAAvD,kBAAA,CAASgE,eAAe,EAAC;MAClC;MAEA,IAAI,CAACT,OAAO,CAAChG,MAAM,EAAE;QACnB;MACF;MAEA0F,aAAa,CAACM,OAAO,GAAGA,OAAO;IACjC;EAAC;IAAA/B,GAAA;IAAA5D,KAAA,EAED,SAAAsG,OAAOA,CAAA,EAAS,CAAC;EAAC;IAAA1C,GAAA;IAAA5D,KAAA,EAElB,SAAAuG,MAAMA,CAAA,EAAS,CAAC;EAAC;IAAA3C,GAAA;IAAA5D,KAAA,EAEjB,SAAAwG,QAAQA,CAAA,EAAS,CAAC;EAAC;IAAA5C,GAAA;IAAA5D,KAAA,EAEnB,SAAAyG,QAAQA,CAAA,EAAS,CAAC;EAAC;IAAA7C,GAAA;IAAA5D,KAAA,EAEnB,SAAA0G,OAAOA,CAACC,KAAY,EAAQ;MAC1B,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,aAAa,GAAG,IAAIC,yBAAe,CAAAzD,aAAA,CAAAA,aAAA,KACnC,IAAI,CAAC2B,OAAO;QACf+B,MAAM,EAAE,IAAAC,6BAAmB,EAAC;UAAEC,UAAU,EAAE,IAAI,CAACjC,OAAO,CAACiC;QAAW,CAAC;MAAC,EACrE,CAAC;IACJ;EAAC;IAAApD,GAAA;IAAA5D,KAAA,EAED,SAAAiH,WAAWA,CAACpE,IAAc,EAAE;MAC1B,IAAM8D,KAAK,GAAG9D,IAAI,CAACqE,MAAM;MACzB,IAAMC,aAAa,GAAG,IAAAC,8BAAyB,EAACvE,IAAI,CAACwE,KAAK,CAAC;MAC3D,IAAMC,OAAO,GAAGX,KAAK,CAACW,OAAO,CAAC,CAAE;MAChC,IAAMC,YAAY,GAAGzB,oBAAI,CAAC0B,QAAQ,CAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,EAAE5E,IAAI,CAAC6E,QAAQ,CAACC,IAAI,CAAC;MACxE,IAAMC,iBAAiB,GAAGL,YAAY,CAAClB,KAAK,CAACP,oBAAI,CAAC+B,GAAG,CAAC;MACtD,IAAMC,YAAY,GAAGF,iBAAiB,CAACG,IAAI,CAAC,GAAG,CAAC;MAChD;MACA,IAAAC,gBAAA,GAA+BrB,KAAK,CAACsB,SAAS,CAAC,CAAC;QAAAC,iBAAA,GAAAlG,QAAA,CAAAgG,gBAAA;QAAhCG,WAAW,GAAAD,iBAAA,CAAAzF,KAAA;MAC3B,IAAM2F,UAAU,GAAGD,WAAW,CAACxI,MAAM,GAAG,CAAC,MAAAqG,MAAA,CAAMmC,WAAW,CAACJ,IAAI,CAAC,GAAG,CAAC,SAAM,EAAE;MAC5E,IAAMM,cAAc,MAAArC,MAAA,CAAM8B,YAAY,OAAA9B,MAAA,CAAIoC,UAAU,EAAApC,MAAA,CAAGnD,IAAI,CAACwE,KAAK,CAAE;MACnE,IAAMiB,MAA2B,GAAG;QAClC3F,IAAI,EAAEwE,aAAa,CAACoB,UAAU;QAC9BC,MAAM,KAAAxC,MAAA,CAAA5D,kBAAA,CAAM+E,aAAa,CAACqB,MAAM,GAAApG,kBAAA,CAAK,IAAAqG,8BAAoB,EAAC,CAAC,EAAC;QAC5DC,KAAK,EAAAtG,kBAAA,CAAM+E,aAAa,CAACuB,KAAK,CAAC;QAC/BC,UAAU,EAAE,EAAE;QACdC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,IAAAC,aAAG,EAACT,cAAc,CAAC;QAC/BU,QAAQ,KAAA/C,MAAA,CAAK8B,YAAY,OAAA9B,MAAA,CAAInD,IAAI,CAAC6E,QAAQ,CAACsB,IAAI,OAAAhD,MAAA,CAAInD,IAAI,CAAC6E,QAAQ,CAACuB,MAAM,CAAE;QACzEhB,SAAS,EAAEL,iBAAiB,CAAC5B,MAAM,CAAA3E,KAAA,CAAxBuG,iBAAiB,EAAAxF,kBAAA,CAAW+F,WAAW;MACpD,CAAC;MAEDG,MAAM,CAACE,MAAM,CAAErF,IAAI,CAAC,IAAA+F,0BAAgB,EAAC,CAAC,CAAC;MACvCZ,MAAM,CAACE,MAAM,CAAErF,IAAI,CAAC,IAAAgG,2BAAiB,EAAC,YAAY,CAAC,CAAC;MACpDb,MAAM,CAACE,MAAM,CAAErF,IAAI,CAAC,IAAAiG,yBAAe,EAAC7B,YAAY,CAAC,CAAC;MAClDe,MAAM,CAACE,MAAM,CAAErF,IAAI,CAAC;QAAER,IAAI,EAAE,WAAW;QAAE3C,KAAK,EAAE2G,KAAK,CAACsB,SAAS,CAAC,CAAC,CAACF,IAAI,CAAC,KAAK;MAAE,CAAC,CAAC;;MAEhF;MACA,IAAI,MAAM,IAAIlF,IAAI,EAAE;QAAA,IAAAwG,IAAA;QAClB,IAAMC,IAAa,GAAGzG,IAAI,CAACyG,IAAI,CAACpD,GAAG,CAAC,UAACqD,GAAG;UAAA,OAAM;YAC5C5G,IAAI,EAAE6G,0BAAS,CAACC,GAAG;YACnBzJ,KAAK,EAAEuJ,GAAG,CAACG,UAAU,CAAC,GAAG,CAAC,GAAGH,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGJ;UAClD,CAAC;QAAA,CAAC,CAAC;QACH,CAAAF,IAAA,GAAAf,MAAM,CAACE,MAAM,EAAErF,IAAI,CAAA9B,KAAA,CAAAgI,IAAA,EAAAjH,kBAAA,CAAIkH,IAAI,EAAC;MAC9B;MAEA,IAAI,aAAa,IAAIzG,IAAI,EAAE;QAAA,IAAA+G,SAAA,GAAApI,0BAAA,CACAqB,IAAI,CAACgH,WAAW;UAAAC,KAAA;QAAA;UAAzC,KAAAF,SAAA,CAAA9H,CAAA,MAAAgI,KAAA,GAAAF,SAAA,CAAArL,CAAA,IAAAwB,IAAA,GAA2C;YAAA,IAAhCgK,UAAU,GAAAD,KAAA,CAAA9J,KAAA;YACnB,IAAI+J,UAAU,CAACC,IAAI,KAAK,MAAM,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO,EAAE;cAC7D;YACF;YAEA,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO,EAAE;cAAA,IAAAC,mBAAA;cAC/B3B,MAAM,CAACI,KAAK,CAAEvF,IAAI,CAChB,IAAA+G,oBAAU,GAAAD,mBAAA,GAAC,IAAI,CAAClF,OAAO,CAAC2D,KAAK,cAAAuB,mBAAA,cAAAA,mBAAA,GAAI,CAAC,CAAC,EAAE;gBACnCD,IAAI,EAAEG,yBAAQ,CAACC,KAAK;gBACpBC,GAAG,EAAEN,UAAU,CAACO;cAClB,CAAC,CACH,CAAC;cACD;YACF;YAEA,IAAIP,UAAU,CAACC,IAAI,KAAK,KAAK,IAAID,UAAU,CAACC,IAAI,KAAK,UAAU,EAAE;cAAA,IAAAO,oBAAA;cAC/DjC,MAAM,CAACI,KAAK,CAAEvF,IAAI,CAChB,IAAA+G,oBAAU,GAAAK,oBAAA,GAAC,IAAI,CAACxF,OAAO,CAAC2D,KAAK,cAAA6B,oBAAA,cAAAA,oBAAA,GAAI,CAAC,CAAC,EAAE;gBACnCP,IAAI,EAAEG,yBAAQ,CAACK,GAAG;gBAClBH,GAAG,EAAEN,UAAU,CAACO;cAClB,CAAC,CACH,CAAC;cACD;YACF;YAEA,IAAIP,UAAU,CAACC,IAAI,KAAK,aAAa,EAAE;cACrC1B,MAAM,CAACgC,WAAW,GAAGP,UAAU,CAACO,WAAW;cAC3C;YACF;YAEA,IAAMG,eAAe,GAAG,IAAAC,qBAAgB,EAACX,UAAU,CAACC,IAAI,EAAED,UAAU,CAACO,WAAW,CAAC;YAEjF,IAAIG,eAAe,EAAE;cACnBnC,MAAM,CAACE,MAAM,CAAErF,IAAI,CAACsH,eAAe,CAAC;cACpC;YACF;YAEAnC,MAAM,CAACM,KAAK,CAAEzF,IAAI,CAAC;cACjBR,IAAI,KAAAqD,MAAA,CAAK+D,UAAU,CAACC,IAAI,QAAAhE,MAAA,CAAK+D,UAAU,CAACO,WAAW,CAAG;cACtDK,MAAM,EAAEC,uBAAM,CAACC,MAAM;cACrBC,KAAK,EAAEC,sBAAK,CAACC,QAAQ;cACrBrC,UAAU,EAAE,EAAE;cACdC,KAAK,EAAE,EAAE;cACTqC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,CAAC;YAClB,CAAC,CAAC;UACJ;QAAC,SAAAC,GAAA;UAAAvB,SAAA,CAAA3L,CAAA,CAAAkN,GAAA;QAAA;UAAAvB,SAAA,CAAAzK,CAAA;QAAA;MACH;MAEA,IAAImI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE3E,IAAI,EAAE;QACjB2F,MAAM,CAACK,UAAU,CAAExF,IAAI,CAAC;UAAER,IAAI,EAAE,SAAS;UAAE3C,KAAK,EAAEsH,OAAO,CAAC3E;QAAK,CAAC,CAAC;MACnE;MAEA,IAAI,CAAA2E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8D,UAAU,IAAG,CAAC,EAAE;QAC3B9C,MAAM,CAACK,UAAU,CAAExF,IAAI,CAAC;UAAER,IAAI,EAAE,YAAY;UAAE3C,KAAK,KAAAgG,MAAA,CAAKnD,IAAI,CAACwI,eAAe,GAAG,CAAC;QAAG,CAAC,CAAC;MACvF;MAEA,IAAMC,QAAQ,GAAG,IAAI,CAAC1E,aAAa,CAAE2E,SAAS,CAACjD,MAAM,CAAC;MAEtD,IAAI,CAACkD,kBAAkB,CAACC,GAAG,CAAC5I,IAAI,CAAC6I,EAAE,EAAEJ,QAAQ,CAAC;MAC9C,IAAI,CAACK,2BAA2B,CAACxI,IAAI,CAACgE,aAAa,CAACoB,UAAU,CAAC;IACjE;EAAC;IAAA3E,GAAA;IAAA5D,KAAA,EAoBD,SAAA4L,WAAWA,CAAC/I,IAAc,EAAEgJ,OAA6B,EAAEC,IAAc,EAAQ;MAC/E,IAAMC,gBAAgB,GAAGD,IAAI,CAACzE,KAAK,KAAK2E,mCAA4B;MACpE,IAAMC,eAAe,GAAGH,IAAI,CAACzE,KAAK,KAAK6E,kCAA2B;MAClE,IAAMC,UAAU,GAAGJ,gBAAgB,IAAIE,eAAe;MACtD,IAAMG,sBAAsB,GAAG,IAAAC,uBAAgB,EAACP,IAAI,CAAC;MACrD,IAAMQ,qBAAqB,GAAG,IAAAC,sBAAe,EAACT,IAAI,CAAC;MACnD,IAAMU,UAAU,GAAGJ,sBAAsB,IAAIE,qBAAqB;MAClE,IAAMhB,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACiB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAE;MAEtD,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACgB,QAAQ,CAACZ,IAAI,CAACa,QAAQ,CAAC,IAAI,CAACH,UAAU,EAAE;QAAA,IAAAI,mBAAA,EAAAC,qBAAA;QACpE,IAAMC,WAAW,IAAAF,mBAAA,GAAG,IAAI,CAAChG,aAAa,cAAAgG,mBAAA,uBAAlBA,mBAAA,CAAoBE,WAAW,CAACxB,QAAQ,CAAC;QAC7D,IAAI,CAACyB,eAAe,CAACtB,GAAG,CAACH,QAAQ,KAAAtF,MAAA,CAAA5D,kBAAA,EAAAyK,qBAAA,GAAO,IAAI,CAACE,eAAe,CAACN,GAAG,CAACnB,QAAQ,CAAC,cAAAuB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,IAAGC,WAAW,EAAC,CAAC;QAChG;MACF;MAEA,IAAIvI,iBAAA,CAAAC,qBAAA,MAAI,EAACwI,iBAAgB,CAAC,CAAAlN,IAAA,CAAtB,IAAI,EAAmBgM,IAAI,GAAG;QAChC;MACF;MAEA,IAAMmB,QAAoB,GAAA7J,aAAA,CAAAA,aAAA,KACrB,IAAA8J,0BAAgB,EAAC,CAAC;QACrBvK,IAAI,EAAEmJ,IAAI,CAACzE,KAAK;QAChB8F,KAAK,EAAErB,IAAI,CAACsB,SAAS,CAACC,OAAO,CAAC,CAAC;QAC/BvC,KAAK,EAAEC,sBAAK,CAACuC,OAAO;QACpBC,IAAI,EAAE,IAAAC,oBAAU,EAAC;MAAC,EACnB;MAED,IAAIhB,UAAU,EAAE;QACd,IAAMiB,KAAK,GAAGrB,sBAAsB,GAChC,IAAI,CAACsB,qBAAqB,CAACjB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,GACvC,IAAI,CAACiC,oBAAoB,CAAClB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAE;QAE3C,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACgB,QAAQ,CAACZ,IAAI,CAACa,QAAQ,CAAC,EAAE;UACrDc,KAAK,CAACG,SAAS,CAACX,QAAQ,CAAC;UAEzB,IAAMY,WAAW,GAAGzB,sBAAsB,GAAG,IAAI,CAAC0B,2BAA2B,GAAG,IAAI,CAACC,0BAA0B;UAE/GN,KAAK,CAACO,UAAU,CAAC,UAACC,UAAU,EAAK;YAAA,IAAAC,gBAAA;YAC/BD,UAAU,CAACtL,IAAI,GAAG,IAAAwL,yBAAkB,EAACF,UAAU,CAACtL,IAAK,CAAC;YACtDsL,UAAU,CAACnD,KAAK,GAAGC,sBAAK,CAACC,QAAQ;YACjC6C,WAAW,CAACpC,GAAG,CAAC5I,IAAI,CAAC6I,EAAE,KAAA1F,MAAA,CAAA5D,kBAAA,EAAA8L,gBAAA,GAAOL,WAAW,CAACpB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,cAAAwC,gBAAA,cAAAA,gBAAA,GAAI,EAAE,IAAA9K,aAAA,CAAAA,aAAA,KAAQ0I,IAAI;cAAEyB,IAAI,EAAEU,UAAU,CAACV;YAAc,IAAG,CAAC;UAC/G,CAAC,CAAC;UACFE,KAAK,CAACW,QAAQ,CAAC,CAAC;UAChB;QACF;QACAX,KAAK,CAACG,SAAS,CAACX,QAAQ,CAAC;QAEzB;MACF;MAEA,IAAId,UAAU,EAAE;QACd,IAAMsB,MAAK,GAAG,IAAIY,2BAAiB,CAAC,CAAC;QACrCZ,MAAK,CAACG,SAAS,CAACX,QAAQ,CAAC;QACzB,IAAIlB,gBAAgB,EAAE;UACpB,IAAI,CAAC2B,qBAAqB,CAACjC,GAAG,CAAC5I,IAAI,CAAC6I,EAAE,EAAE+B,MAAK,CAAC;QAChD,CAAC,MAAM;UACL,IAAI,CAACE,oBAAoB,CAAClC,GAAG,CAAC5I,IAAI,CAAC6I,EAAE,EAAE+B,MAAK,CAAC;QAC/C;QACA;MACF;MAEA,IAAI,CAAC7G,aAAa,CAAEgH,SAAS,CAACtC,QAAQ,EAAEgD,SAAS,EAAErB,QAAQ,CAAC;IAC9D;EAAC;IAAArJ,GAAA;IAAA5D,KAAA,EAED,SAAAuO,SAASA,CAAC1L,IAAc,EAAEgJ,OAA6B,EAAEC,IAAc,EAAQ;MAC7E,IAAIvH,iBAAA,CAAAC,qBAAA,MAAI,EAACwI,iBAAgB,CAAC,CAAAlN,IAAA,CAAtB,IAAI,EAAmBgM,IAAI,GAAG;QAChC;MACF;MACA;MACA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACY,QAAQ,CAACZ,IAAI,CAACa,QAAQ,CAAC,EAAE;QACrD;MACF;MACA,IAAMrB,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACiB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAE;MACtD,IAAMK,gBAAgB,GAAGD,IAAI,CAACzE,KAAK,KAAK2E,mCAA4B;MACpE,IAAMC,eAAe,GAAGH,IAAI,CAACzE,KAAK,KAAK6E,kCAA2B;MAClE,IAAME,sBAAsB,GAAG,IAAAC,uBAAgB,EAACP,IAAI,CAAC;MACrD,IAAMQ,qBAAqB,GAAG,IAAAC,sBAAe,EAACT,IAAI,CAAC;MACnD,IAAM0C,WAAW,GAAGvC,eAAe,IAAIK,qBAAqB;MAC5D,IAAMmC,MAAM,GAAG1C,gBAAgB,IAAIE,eAAe,IAAIG,sBAAsB,IAAIE,qBAAqB;MAErG,IAAImC,MAAM,EAAE;QACV,IAAMhB,KAAK,GAAGe,WAAW,GAAG,IAAI,CAACb,oBAAoB,CAAClB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,GAAI,IAAI,CAACgC,qBAAqB,CAACjB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAE;QAE9G+B,KAAK,CAACO,UAAU,CAAC,UAACC,UAAU,EAAK;UAAA,IAAAS,qBAAA;UAC/B,IAAAC,KAAA,IAAAD,qBAAA,GAAmC,IAAAE,gCAAsB,EAACX,UAAU,CAACrF,KAAK,CAAC,cAAA8F,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;YAAAG,YAAA,GAAAF,KAAA,CAAzEhE,MAAM;YAANA,MAAM,GAAAkE,YAAA,cAAGjE,uBAAM,CAACC,MAAM,GAAAgE,YAAA;UAC9BZ,UAAU,CAACtD,MAAM,GAAGmB,IAAI,CAACgD,KAAK,GAAGlE,uBAAM,CAACmE,MAAM,GAAGpE,MAAM;UACvDsD,UAAU,CAACnD,KAAK,GAAGC,sBAAK,CAACC,QAAQ;UACjC,IAAIc,IAAI,CAACgD,KAAK,EAAE;YACdb,UAAU,CAAC/C,aAAa,GAAA9H,aAAA,KAAQ,IAAA4L,gCAA2B,EAAClD,IAAI,CAACgD,KAAK,CAAC,CAAE;UAC3E;QACF,CAAC,CAAC;QACFrB,KAAK,CAACW,QAAQ,CAAC;UACba,QAAQ,EAAEnD,IAAI,CAACmD;QACjB,CAAC,CAAC;QACF;MACF;MAEA,IAAMnC,WAAW,GAAG,IAAI,CAAClG,aAAa,CAAEkG,WAAW,CAACxB,QAAQ,CAAC;MAE7D,IAAI,CAACwB,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAAClG,aAAa,CAAEoH,UAAU,CAAClB,WAAW,EAAE,UAACmB,UAAU,EAAK;QAAA,IAAAiB,sBAAA;QAC1D,IAAAC,KAAA,IAAAD,sBAAA,GAAmC,IAAAN,gCAAsB,EAACX,UAAU,CAACrF,KAAK,CAAC,cAAAsG,sBAAA,cAAAA,sBAAA,GAAI,CAAC,CAAC;UAAAE,YAAA,GAAAD,KAAA,CAAzExE,MAAM;UAANA,MAAM,GAAAyE,YAAA,cAAGxE,uBAAM,CAACC,MAAM,GAAAuE,YAAA;QAC9BnB,UAAU,CAACtD,MAAM,GAAGmB,IAAI,CAACgD,KAAK,GAAGlE,uBAAM,CAACmE,MAAM,GAAGpE,MAAM;QACvDsD,UAAU,CAACnD,KAAK,GAAGC,sBAAK,CAACC,QAAQ;QACjC,IAAIc,IAAI,CAACgD,KAAK,EAAE;UACdb,UAAU,CAAC/C,aAAa,GAAA9H,aAAA,KAAQ,IAAA4L,gCAA2B,EAAClD,IAAI,CAACgD,KAAK,CAAC,CAAE;QAC3E;MACF,CAAC,CAAC;MACF,IAAI,CAAClI,aAAa,CAAEwH,QAAQ,CAACtB,WAAW,EAAE;QAAEmC,QAAQ,EAAEnD,IAAI,CAACmD;MAAS,CAAC,CAAC;IACxE;EAAC;IAAArL,GAAA;IAAA5D,KAAA;MAAA,IAAAqP,UAAA,GAAAlO,iBAAA,cAAAhD,YAAA,GAAAqC,CAAA,CAED,SAAA8O,QAAgBzM,IAAc,EAAEyF,MAA4B;QAAA,IAAAiH,KAAA;UAAAC,qBAAA;UAAAC,qBAAA;UAAAC,sBAAA;QAAA,IAAApE,QAAA,EAAAqE,QAAA,EAAAC,MAAA,EAAAd,KAAA,EAAAe,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAA7H,WAAA,EAAA8H,gBAAA,EAAAC,eAAA,EAAAC,wBAAA,EAAAC,uBAAA,EAAArD,eAAA,EAAAsD,mBAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,UAAA,EAAApS,CAAA,EAAAqS,UAAA,EAAAC,QAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAtD,UAAA,EAAAuD,QAAA;QAAA,OAAArT,YAAA,GAAAoC,CAAA,WAAAkR,QAAA;UAAA,kBAAAA,QAAA,CAAAlT,CAAA;YAAA;cACpD+M,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACiB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,EACrD;cACMiE,QAAQ,GAAGrH,MAAM,CAACoJ,aAAa,KAAKpD,SAAS,GAAGhG,MAAM,CAACoJ,aAAa,GAAGpJ,MAAM,CAACqJ,WAAW;cACzF/B,MAAM,UAAA5J,MAAA,CAAU4L,uBAAO,CAACC,GAAG,cAAA7L,MAAA,CAAW2J,QAAQ;cAC9Cb,KAAK,GAAGxG,MAAM,CAACwG,KAAK,EAC1B;cAAAe,qBAAA,GAC8DhN,IAAI,CAACqE,MAAM,CAACe,SAAS,CAAC,CAAC,EAAA6H,sBAAA,GAAA9N,QAAA,CAAA6N,qBAAA,GAA5EE,iBAAiB,GAAAD,sBAAA,KAAEE,cAAc,GAAAF,sBAAA,KAAK3H,WAAW,GAAA2H,sBAAA,CAAArN,KAAA;cACpDwN,gBAAgB,GAAG,IAAI,CAACvC,qBAAqB,CAACjB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC;cAC1DwE,eAAe,GAAG,IAAI,CAACvC,oBAAoB,CAAClB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC;cAE9D,IAAI,CAAC9E,aAAa,CAAEkL,UAAU,CAACxG,QAAQ,EAAE,UAACyG,UAAU,EAAK;gBACvDA,UAAU,CAACvJ,MAAM,CAACrF,IAAI,CAAC,IAAA6O,sBAAY,EAAC,CAAC,CAAC;gBACtCD,UAAU,CAACvJ,MAAM,CAACrF,IAAI,CAAC,IAAA8O,wBAAc,EAACrC,MAAM,CAAC,CAAC;gBAE9C,IAAIG,iBAAiB,IAAI,CAAC,IAAAmC,aAAQ,EAACH,UAAU,EAAEvI,0BAAS,CAAC2I,YAAY,CAAC,EAAE;kBACtEJ,UAAU,CAACvJ,MAAM,CAACrF,IAAI,CAAC;oBAAER,IAAI,EAAE6G,0BAAS,CAAC2I,YAAY;oBAAEnS,KAAK,EAAE+P;kBAAkB,CAAC,CAAC;gBACpF;gBAEA,IAAIR,KAAI,CAACxK,OAAO,CAACC,UAAU,IAAIgL,cAAc,IAAI,CAAC,IAAAkC,aAAQ,EAACH,UAAU,EAAEvI,0BAAS,CAAC4I,KAAK,CAAC,EAAE;kBACvFL,UAAU,CAACvJ,MAAM,CAACrF,IAAI,CAAC;oBAAER,IAAI,EAAE6G,0BAAS,CAAC4I,KAAK;oBAAEpS,KAAK,EAAEgQ;kBAAe,CAAC,CAAC;gBAC1E;gBAEA,IAAI7H,WAAW,CAACxI,MAAM,GAAG,CAAC,IAAI,CAAC,IAAAuS,aAAQ,EAACH,UAAU,EAAEvI,0BAAS,CAAC6I,SAAS,CAAC,EAAE;kBACxEN,UAAU,CAACvJ,MAAM,CAACrF,IAAI,CAAC;oBAAER,IAAI,EAAE6G,0BAAS,CAAC6I,SAAS;oBAAErS,KAAK,EAAEmI,WAAW,CAACJ,IAAI,CAAC,KAAK;kBAAE,CAAC,CAAC;gBACvF;gBAEA,IAAI+G,KAAK,EAAE;kBACTiD,UAAU,CAAC7G,aAAa,GAAA9H,aAAA,KAAQ,IAAA4L,gCAA2B,EAACF,KAAK,CAAC,CAAE;gBACtE,CAAC,MAAM;kBAAA,IAAAwD,iBAAA;kBACL,IAAMC,UAAU,IAAAD,iBAAA,GAAGzP,IAAI,CAACgH,WAAW,cAAAyI,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAkBE,IAAI,CACvC,UAACzI,UAAU;oBAAA,OAAKA,UAAU,CAACC,IAAI,KAAK,MAAM,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO;kBAAA,CAC3E,CAAC,cAAAsI,iBAAA,uBAFkBA,iBAAA,CAEhBhI,WAAW;kBAEd,IAAIiI,UAAU,EAAE;oBACdR,UAAU,CAAC7G,aAAa,GAAA9H,aAAA,CAAAA,aAAA,KAAQ2O,UAAU,CAAC7G,aAAa;sBAAEuH,OAAO,EAAEF;oBAAU,EAAE;kBACjF;gBACF;gBAEAR,UAAU,CAACpH,MAAM,GAAG,IAAA+H,0BAAmB,EAACpK,MAAM,CAACqC,MAAM,EAAE9H,IAAI,CAAC8P,cAAc,CAAC;gBAC3EZ,UAAU,CAACjH,KAAK,GAAGC,sBAAK,CAACC,QAAQ;cACnC,CAAC,CAAC;cAEImF,wBAAwB,IAAAX,qBAAA,GAAG,IAAI,CAAC1B,2BAA2B,CAACrB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,cAAA8D,qBAAA,cAAAA,qBAAA,GAAI,EAAE;cAC9EY,uBAAuB,IAAAX,qBAAA,GAAG,IAAI,CAAC1B,0BAA0B,CAACtB,GAAG,CAAC5J,IAAI,CAAC6I,EAAE,CAAC,cAAA+D,qBAAA,cAAAA,qBAAA,GAAI,EAAE;cAC5E1C,eAAe,IAAA2C,sBAAA,GAAG,IAAI,CAAC3C,eAAe,CAACN,GAAG,CAACnB,QAAQ,CAAC,cAAAoE,sBAAA,cAAAA,sBAAA,GAAI,EAAE;cAE1DW,mBAAmB,GAAG,IAAIvL,GAAG,CAAyE,CAAC;cAEzGwL,eAAe,GAAG,CAAC;cAAAC,UAAA,GAAA/O,0BAAA,CAEA2O,wBAAwB;cAAA;gBAA/C,KAAAI,UAAA,CAAAzO,CAAA,MAAA0O,MAAA,GAAAD,UAAA,CAAAhS,CAAA,IAAAwB,IAAA,GAAiD;kBAAtCsR,SAAQ,GAAAb,MAAA,CAAAxQ,KAAA;kBACjBqQ,mBAAmB,CAAC5E,GAAG,CAAC6E,eAAe,EAAE;oBACvCM,QAAQ,EAAES,SAAQ,CAAC9D,IAAI;oBACvBkB,MAAM,EAAE,IAAI;oBACZ4C,QAAQ,EAARA;kBACF,CAAC,CAAC;kBACFf,eAAe,EAAE;gBACnB;cAAC,SAAAnF,GAAA;gBAAAoF,UAAA,CAAAtS,CAAA,CAAAkN,GAAA;cAAA;gBAAAoF,UAAA,CAAApR,CAAA;cAAA;cAAAuR,UAAA,GAAAlP,0BAAA,CAEsBuL,eAAe;cAAA;gBAAtC,KAAA2D,UAAA,CAAA5O,CAAA,MAAA6O,MAAA,GAAAD,UAAA,CAAAnS,CAAA,IAAAwB,IAAA,GAAwC;kBAA7B6Q,QAAQ,GAAAD,MAAA,CAAA3Q,KAAA;kBACjBqQ,mBAAmB,CAAC5E,GAAG,CAAC6E,eAAe,EAAE;oBACvCM,QAAQ,EAARA,QAAQ;oBACRnC,MAAM,EAAE;kBACV,CAAC,CAAC;kBACF6B,eAAe,EAAE;gBACnB;cAAC,SAAAnF,GAAA;gBAAAuF,UAAA,CAAAzS,CAAA,CAAAkN,GAAA;cAAA;gBAAAuF,UAAA,CAAAvR,CAAA;cAAA;cAAA0R,UAAA,GAAArP,0BAAA,CAEsB4O,uBAAuB;cAAA;gBAA9C,KAAAS,UAAA,CAAA/O,CAAA,MAAAgP,MAAA,GAAAD,UAAA,CAAAtS,CAAA,IAAAwB,IAAA,GAAgD;kBAArCsR,UAAQ,GAAAP,MAAA,CAAA9Q,KAAA;kBACjBqQ,mBAAmB,CAAC5E,GAAG,CAAC6E,eAAe,EAAE;oBACvCM,QAAQ,EAAES,UAAQ,CAAC9D,IAAI;oBACvBkB,MAAM,EAAE,IAAI;oBACZ4C,QAAQ,EAARA;kBACF,CAAC,CAAC;kBACFf,eAAe,EAAE;gBACnB;cAAC,SAAAnF,GAAA;gBAAA0F,UAAA,CAAA5S,CAAA,CAAAkN,GAAA;cAAA;gBAAA0F,UAAA,CAAA1R,CAAA;cAAA;cAEQR,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAG2J,MAAM,CAAC2C,WAAW,CAACtL,MAAM;gBAAA8R,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cACrCyS,UAAU,GAAG1I,MAAM,CAAC2C,WAAW,CAACtM,CAAC,CAAC;cAClCsS,QAAQ,GAAGZ,mBAAmB,CAAC5D,GAAG,CAAC9N,CAAC,CAAC;cAAA,MAEvCsS,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAExC,MAAM;gBAAAgD,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cAAA,OAAAkT,QAAA,CAAAjS,CAAA;YAAA;cAAA,MAETyR,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEL,QAAQ;gBAAAa,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cAAAkT,QAAA,CAAAlT,CAAA;cAAA,OACrB,IAAI,CAACqU,iBAAiB,CAACtH,QAAQ,EAAE2F,QAAQ,CAACL,QAAQ,EAAEI,UAAU,CAAC;YAAA;cAAAS,QAAA,CAAAlT,CAAA;cAAA;YAAA;cAAAkT,QAAA,CAAAlT,CAAA;cAAA,OAE/D,IAAI,CAACqU,iBAAiB,CAACtH,QAAQ,EAAEgD,SAAS,EAAE0C,UAAU,CAAC;YAAA;cATlBrS,CAAC,EAAE;cAAA8S,QAAA,CAAAlT,CAAA;cAAA;YAAA;cAalD,IAAI+J,MAAM,CAACuK,MAAM,CAAClT,MAAM,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAACiH,aAAa,CAAEkM,eAAe,CACjCxH,QAAQ,EACRgD,SAAS,EACT,QAAQ,EACRyE,MAAM,CAACnQ,IAAI,CAAC,IAAAoQ,cAAS,EAAC1K,MAAM,CAACuK,MAAM,CAAC9K,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EACvD;kBACEkL,WAAW,EAAEC,4BAAW,CAACC;gBAC3B,CACF,CAAC;cACH;cAEA,IAAI7K,MAAM,CAAC8K,MAAM,CAACzT,MAAM,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAACiH,aAAa,CAAEkM,eAAe,CACjCxH,QAAQ,EACRgD,SAAS,EACT,QAAQ,EACRyE,MAAM,CAACnQ,IAAI,CAAC,IAAAoQ,cAAS,EAAC1K,MAAM,CAAC8K,MAAM,CAACrL,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EACvD;kBACEkL,WAAW,EAAEC,4BAAW,CAACC;gBAC3B,CACF,CAAC;cACH;;cAEA;cACA;cAESxU,EAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,EAAC,GAAG2J,MAAM,CAAC2C,WAAW,CAACtL,MAAM;gBAAA8R,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cACrCyS,WAAU,GAAG1I,MAAM,CAAC2C,WAAW,CAACtM,EAAC,CAAC;cAClCsS,SAAQ,GAAGZ,mBAAmB,CAAC5D,GAAG,CAAC9N,EAAC,CAAC;cAAA,MAEvCsS,SAAQ,aAARA,SAAQ,eAARA,SAAQ,CAAExC,MAAM,IAAIwC,SAAQ,CAACI,QAAQ;gBAAAI,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cACjC8S,QAAQ,GAAGJ,SAAQ,CAACI,QAAQ;cAC5BC,YAAY,GAAGnB,wBAAwB,CAACzD,QAAQ,CAAC2E,QAAQ,CAAC;cAC1DE,WAAW,GAAGD,YAAY,GAAGrB,gBAAgB,GAAGC,eAAe;cAAA,MAEjEc,WAAU,CAACiC,WAAW,KAAKI,6CAAmC;gBAAA5B,QAAA,CAAAlT,CAAA;gBAAA;cAAA;cAAAkT,QAAA,CAAAlT,CAAA;cAAA,OAC1D,IAAI,CAACqU,iBAAiB,CAACtH,QAAQ,EAAE+F,QAAQ,CAAC9D,IAAI,EAAEyD,WAAU,CAAC;YAAA;cAAA,OAAAS,QAAA,CAAAjS,CAAA;YAAA;cAInE,IAAI+R,WAAW,EAAE;gBACTtD,UAAU,GAAGsD,WAAW,CAAC+B,cAAc,CAACjC,QAAQ,CAAC9D,IAAI,CAAC;gBAC5D,IAAIU,UAAU,EAAE;kBACRuD,QAAQ,GAAGD,WAAW,CAACgC,aAAa,CAACvC,WAAU,EAAE,IAAI,CAACpK,aAAa,CAAEE,MAAM,CAAC;kBAClFmH,UAAU,CAAChD,WAAW,CAAC9H,IAAI,CAAC;oBAC1BR,IAAI,EAAEqO,WAAU,CAACrO,IAAI;oBACrBqH,IAAI,EAAEgH,WAAU,CAACiC,WAAW;oBAC5BO,MAAM,EAAEhC;kBACV,CAAC,CAAC;gBACJ;cACF;YAAC;cAxB0C7S,EAAC,EAAE;cAAA8S,QAAA,CAAAlT,CAAA;cAAA;YAAA;cA4BlD,IAAI,CAACqI,aAAa,CAAEkL,UAAU,CAACxG,QAAQ,EAAE,UAACyG,UAAU,EAAK;gBACvD,IAAM0B,YAAY,GAAG1B,UAAU,CAACvJ,MAAM,CAACkL,MAAM,CAA0B,UAACC,GAAG,EAAEC,KAAK,EAAK;kBACrF,IAAI,CAACD,GAAG,CAACC,KAAK,CAACjR,IAAI,CAAC,EAAE;oBACpBgR,GAAG,CAACC,KAAK,CAACjR,IAAI,CAAC,GAAG,EAAE;kBACtB;kBAEAgR,GAAG,CAACC,KAAK,CAACjR,IAAI,CAAC,CAACQ,IAAI,CAACyQ,KAAK,CAAC;kBAE3B,OAAOD,GAAG;gBACZ,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,IAAME,SAAS,GAAG7U,MAAM,CAAC+D,IAAI,CAAC0Q,YAAY,CAAC,CAACK,OAAO,CAAC,UAACC,SAAS,EAAK;kBACjE,IAAMC,WAAW,GAAGP,YAAY,CAACM,SAAS,CAAC;kBAE3C,IACEA,SAAS,KAAKvK,0BAAS,CAAC4I,KAAK,IAC7B2B,SAAS,KAAKvK,0BAAS,CAAC2I,YAAY,IACpC4B,SAAS,KAAKvK,0BAAS,CAAC6I,SAAS,EACjC;oBACA,OAAO2B,WAAW,CAACvR,KAAK,CAAC,CAAC,CAAC,CAAC;kBAC9B;kBAEA,OAAOuR,WAAW;gBACpB,CAAC,CAAC;gBAEF,IAAI/D,gBAAgB,EAAE;kBAAA,IAAAgE,iBAAA;kBACpB,CAAAA,iBAAA,GAAAlC,UAAU,CAACnJ,KAAK,EAACsL,OAAO,CAAA7S,KAAA,CAAA4S,iBAAA,EAAA7R,kBAAA,CAAI6N,gBAAgB,CAACrH,KAAK,EAAC;kBACnD2G,KAAI,CAAC7B,qBAAqB,UAAO,CAAC7K,IAAI,CAAC6I,EAAE,CAAC;gBAC5C;gBAEA,IAAIwE,eAAe,EAAE;kBAAA,IAAAiE,kBAAA;kBACnB,CAAAA,kBAAA,GAAApC,UAAU,CAACnJ,KAAK,EAACzF,IAAI,CAAA9B,KAAA,CAAA8S,kBAAA,EAAA/R,kBAAA,CAAI8N,eAAe,CAACtH,KAAK,EAAC;kBAC/C2G,KAAI,CAAC5B,oBAAoB,UAAO,CAAC9K,IAAI,CAAC6I,EAAE,CAAC;gBAC3C;gBAEAqG,UAAU,CAACvJ,MAAM,GAAGqL,SAAS;cAC/B,CAAC,CAAC;cACF,IAAI,CAACjN,aAAa,CAAEwN,QAAQ,CAAC9I,QAAQ,EAAE;gBAAE2D,QAAQ,EAAE3G,MAAM,CAAC2G;cAAS,CAAC,CAAC;cACrE,IAAI,CAACrI,aAAa,CAAEyN,SAAS,CAAC/I,QAAQ,CAAC;YAAC;cAAA,OAAAmG,QAAA,CAAAjS,CAAA;UAAA;QAAA,GAAA8P,OAAA;MAAA,CACzC;MAAA,SAvLKgF,SAASA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAnF,UAAA,CAAAhO,KAAA,OAAAD,SAAA;MAAA;MAAA,OAATkT,SAAS;IAAA;EAAA;IAAA1Q,GAAA;IAAA5D,KAAA;MAAA,IAAAyU,kBAAA,GAAAtT,iBAAA,cAAAhD,YAAA,GAAAqC,CAAA,CAyLf,SAAAkU,SAAA;QAAA,IAAAC,MAAA;QAAA,IAAAC,gBAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,OAAA7W,YAAA,GAAAoC,CAAA,WAAA0U,SAAA;UAAA,kBAAAA,SAAA,CAAA1W,CAAA;YAAA;cACQqW,gBAAgB,GAAG,IAAI,CAACjO,KAAK,CAACuO,QAAQ,CAAC,CAAC,CAACjS,MAAM,CAAC,UAAAkS,KAAA,EAAe;gBAAA,IAAZ9N,KAAK,GAAA8N,KAAA,CAAL9N,KAAK;gBAC5D,IAAMF,aAAa,GAAG,IAAAC,8BAAyB,EAACC,KAAK,CAAC;gBAEtD,OAAO,CAACsN,MAAI,CAAChJ,2BAA2B,CAACe,QAAQ,CAACvF,aAAa,CAACoB,UAAU,CAAC;cAC7E,CAAC,CAAC;cAAAsM,UAAA,GAAArT,0BAAA,CAEqBoT,gBAAgB;cAAAK,SAAA,CAAA7V,CAAA;cAAAyV,UAAA,CAAA/S,CAAA;YAAA;cAAA,KAAAgT,MAAA,GAAAD,UAAA,CAAAtW,CAAA,IAAAwB,IAAA;gBAAAkV,SAAA,CAAA1W,CAAA;gBAAA;cAAA;cAA5BwW,QAAQ,GAAAD,MAAA,CAAA9U,KAAA;cACjB,IAAI,CAACiH,WAAW,CAAC8N,QAAQ,CAAC;cAACE,SAAA,CAAA1W,CAAA;cAAA,OACrB,IAAI,CAAC+V,SAAS,CAACS,QAAQ,EAAE;gBAC7BpK,MAAM,EAAEC,uBAAM,CAACwK,OAAO;gBACtBnK,WAAW,EAAE,EAAE;gBACfgE,QAAQ,EAAE,CAAC;gBACXoG,MAAM,EAAE,EAAE;gBACV3D,aAAa,EAAE,CAAC;gBAChBC,WAAW,EAAE,CAAC;gBACd2D,KAAK,EAAE,CAAC;gBACR1M,KAAK,EAAE,EAAE;gBACTwK,MAAM,EAAE,EAAE;gBACVP,MAAM,EAAE,EAAE;gBACVzF,SAAS,EAAE,IAAI,CAACmI,eAAe;gBAC/B1L,WAAW,EAAE;cACf,CAAC,CAAC;YAAA;cAAAoL,SAAA,CAAA1W,CAAA;cAAA;YAAA;cAAA0W,SAAA,CAAA1W,CAAA;cAAA;YAAA;cAAA0W,SAAA,CAAA7V,CAAA;cAAA4V,EAAA,GAAAC,SAAA,CAAA1V,CAAA;cAAAsV,UAAA,CAAA5W,CAAA,CAAA+W,EAAA;YAAA;cAAAC,SAAA,CAAA7V,CAAA;cAAAyV,UAAA,CAAA1V,CAAA;cAAA,OAAA8V,SAAA,CAAA9V,CAAA;YAAA;cAAA,OAAA8V,SAAA,CAAAzV,CAAA;UAAA;QAAA,GAAAkV,QAAA;MAAA,CAEL;MAAA,SAxBKc,iBAAiBA,CAAA;QAAA,OAAAf,kBAAA,CAAApT,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAjBoU,iBAAiB;IAAA;EAAA;IAAA5R,GAAA;IAAA5D,KAAA;MAAA,IAAAyV,MAAA,GAAAtU,iBAAA,cAAAhD,YAAA,GAAAqC,CAAA,CA0BvB,SAAAkV,SAAA;QAAA,OAAAvX,YAAA,GAAAoC,CAAA,WAAAoV,SAAA;UAAA,kBAAAA,SAAA,CAAApX,CAAA;YAAA;cAAAoX,SAAA,CAAApX,CAAA;cAAA,OACQ,IAAI,CAACiX,iBAAiB,CAAC,CAAC;YAAA;cAE9B,IAAI,CAAC5O,aAAa,CAAEgP,oBAAoB,CAAC,CAAC;cAC1C,IAAI,CAAChP,aAAa,CAAEiP,0BAA0B,CAAC,CAAC;YAAC;cAAA,OAAAF,SAAA,CAAAnW,CAAA;UAAA;QAAA,GAAAkW,QAAA;MAAA,CAClD;MAAA,SALKI,KAAKA,CAAA;QAAA,OAAAL,MAAA,CAAApU,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAL0U,KAAK;IAAA;EAAA;IAAAlS,GAAA;IAAA5D,KAAA,EAOX,SAAA+V,aAAaA,CAAA,EAAY;MACvB,OAAO,KAAK;IACd;EAAC;IAAAnS,GAAA;IAAA5D,KAAA,EAED,SAAQgW,0BAA0BA,CAACC,kBAA0B,EAAExD,OAAmC,EAAE;MAClG,IAAAyD,aAAA,GAAkCzD,OAAO,CAAC0D,IAAI;QAAtCxT,IAAI,GAAAuT,aAAA,CAAJvT,IAAI;QAAAyT,qBAAA,GAAAF,aAAA,CAAEvN,UAAU;QAAVA,UAAU,GAAAyN,qBAAA,cAAG,EAAE,GAAAA,qBAAA;MAE7B,IAAI,CAACxP,aAAa,CAAEoH,UAAU,CAACiI,kBAAkB,EAAE,UAACnK,IAAI,EAAK;QAAA,IAAAuK,gBAAA;QAC3D,IAAI1T,IAAI,EAAE;UACRmJ,IAAI,CAACnJ,IAAI,GAAGA,IAAI;QAClB;QAEA,CAAA0T,gBAAA,GAAAvK,IAAI,CAACnD,UAAU,EAACxF,IAAI,CAAA9B,KAAA,CAAAgV,gBAAA,EAAAjU,kBAAA,CAAIuG,UAAU,EAAC;MACrC,CAAC,CAAC;IACJ;EAAC;IAAA/E,GAAA;IAAA5D,KAAA;MAAA,IAAAsW,kBAAA,GAAAnV,iBAAA,cAAAhD,YAAA,GAAAqC,CAAA,CAED,SAAA+V,SACEjL,QAAgB,EAChB2K,kBAAsC,EACtCjF,UAKC;QAAA,IAAAwF,oBAAA,EAAA/D,OAAA,EAAAgE,UAAA,EAAAxD,WAAA,EAAAyD,cAAA,EAAAC,YAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,QAAA;QAAA,OAAA3Y,YAAA,GAAAoC,CAAA,WAAAwW,SAAA;UAAA,kBAAAA,SAAA,CAAAxY,CAAA;YAAA;cAAA,MAEG,CAACyS,UAAU,CAACgG,IAAI,IAAI,CAAChG,UAAU,CAAClL,IAAI;gBAAAiR,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAAA,OAAAwY,SAAA,CAAAvX,CAAA;YAAA;cAIlCgX,oBAAoB,GAAGxF,UAAU,CAACiC,WAAW,KAAKI,6CAAmC;cAAA,MAEvFmD,oBAAoB,IAAI,CAACxF,UAAU,CAACgG,IAAI;gBAAAD,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAAA,OAAAwY,SAAA,CAAAvX,CAAA;YAAA;cAAA,KAIxCgX,oBAAoB;gBAAAO,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAChBkU,OAAO,GAAGwE,IAAI,CAACC,KAAK,CAAClG,UAAU,CAACgG,IAAI,CAAExU,QAAQ,CAAC,CAAC,CAAC;cAAA,MAEnDiQ,OAAO,CAACzI,IAAI,KAAK,eAAe;gBAAA+M,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAClC,IAAI,CAACyX,0BAA0B,CAACC,kBAAkB,EAAGxD,OAAO,CAAC;cAAC,OAAAsE,SAAA,CAAAvX,CAAA;YAAA;cAIhE,IAAI,CAACoH,aAAa,CAAEuQ,oBAAoB,CAAC7L,QAAQ,EAAE,CAACmH,OAAO,CAAC,CAAC;cAAC,OAAAsE,SAAA,CAAAvX,CAAA;YAAA;cAI1DiX,UAAU,GAAG,IAAI,CAAC7P,aAAa,CAAEgH,SAAS,CAACtC,QAAQ,EAAE2K,kBAAkB,EAAE;gBAAEtT,IAAI,EAAEqO,UAAU,CAACrO;cAAK,CAAC,CAAC,EAEzG;cACA;cACA;cACA,IAAI8T,UAAU,EAAE;gBACd,IAAI,CAAC7P,aAAa,CAAEwH,QAAQ,CAACqI,UAAU,EAAEnI,SAAS,CAAC;cACrD;cAAC,KAEG0C,UAAU,CAACgG,IAAI;gBAAAD,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cACjB,IAAI,CAACqI,aAAa,CAAEkM,eAAe,CAACxH,QAAQ,EAAEmL,UAAU,EAAEzF,UAAU,CAACrO,IAAI,EAAEqO,UAAU,CAACgG,IAAI,EAAE;gBAC1F/D,WAAW,EAAEjC,UAAU,CAACiC;cAC1B,CAAC,CAAC;cAAC8D,SAAA,CAAAxY,CAAA;cAAA;YAAA;cAAA,IACO,IAAA6Y,kBAAU,EAACpG,UAAU,CAAClL,IAAK,CAAC;gBAAAiR,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAAA,OAAAwY,SAAA,CAAAvX,CAAA;YAAA;cAGhCyT,WAAW,GACfjC,UAAU,CAACrO,IAAI,KAAK,OAAO,IAAIqO,UAAU,CAACiC,WAAW,KAAK,iBAAiB,GACvE,yCAAyC,GACzCjC,UAAU,CAACiC,WAAW;cAE5B,IAAI,CAACrM,aAAa,CAAEkM,eAAe,CAACxH,QAAQ,EAAEmL,UAAU,EAAEzF,UAAU,CAACrO,IAAI,EAAEqO,UAAU,CAAClL,IAAI,EAAG;gBAC3FmN,WAAW,EAAXA;cACF,CAAC,CAAC;YAAC;cAAA,IAGAjC,UAAU,CAACrO,IAAI,CAAC0U,KAAK,CAACC,oBAAa,CAAC;gBAAAP,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAAA,OAAAwY,SAAA,CAAAvX,CAAA;YAAA;cAInCkX,cAAc,GAAG1F,UAAU,CAAClL,IAAI,CAAEK,OAAO,CAACmR,oBAAa,EAAE,EAAE,CAAC;cAAA,KAE9D,IAAI,CAACC,cAAc,CAAC7K,QAAQ,CAACgK,cAAc,CAAC;gBAAAK,SAAA,CAAAxY,CAAA;gBAAA;cAAA;cAAA,OAAAwY,SAAA,CAAAvX,CAAA;YAAA;cAAAuX,SAAA,CAAAxY,CAAA;cAAA,OAIrB,IAAAiZ,2BAAiB,KAAAxR,MAAA,CAAI0Q,cAAc,gBAAa,CAAC;YAAA;cAAtEC,YAAY,GAAAI,SAAA,CAAAxX,CAAA;cAAAwX,SAAA,CAAAxY,CAAA;cAAA,OACW,IAAAiZ,2BAAiB,KAAAxR,MAAA,CAAI0Q,cAAc,kBAAe,CAAC;YAAA;cAA1EE,cAAc,GAAAG,SAAA,CAAAxX,CAAA;cAAAwX,SAAA,CAAAxY,CAAA;cAAA,OACK,IAAAiZ,2BAAiB,KAAAxR,MAAA,CAAI0Q,cAAc,cAAW,CAAC;YAAA;cAAlEG,UAAU,GAAAE,SAAA,CAAAxX,CAAA;cACVuX,QAAQ,GAAG9F,UAAU,CAACrO,IAAI,CAACwD,OAAO,CAACmR,oBAAa,EAAE,EAAE,CAAC;cAE3D,IAAI,CAAC1Q,aAAa,CAAEkM,eAAe,CACjCxH,QAAQ,EACRgD,SAAS,EACTwI,QAAQ,EACR/D,MAAM,CAACnQ,IAAI,CACTqU,IAAI,CAACQ,SAAS,CAAC;gBACbC,QAAQ,EAAEd,cAAc;gBACxBe,MAAM,EAAEhB,YAAY;gBACpBiB,IAAI,EAAEf,UAAU;gBAChBlU,IAAI,EAAEmU;cACR,CAAwB,CAAC,EACzB,OACF,CAAC,EACD;gBACE7D,WAAW,EAAEC,4BAAW,CAAC2E,SAAS;gBAClCC,aAAa,EAAE;cACjB,CACF,CAAC;cAED,IAAI,CAACP,cAAc,CAACpU,IAAI,CAACuT,cAAc,CAAC;YAAC;cAAA,OAAAK,SAAA,CAAAvX,CAAA;UAAA;QAAA,GAAA+W,QAAA;MAAA,CAC1C;MAAA,SA7Fa3D,iBAAiBA,CAAAmF,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAA3B,kBAAA,CAAAjV,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAjBwR,iBAAiB;IAAA;EAAA;IAAAhP,GAAA;IAAA5D,KAAA,EA+F/B,SAAAkY,OAAOA,CAAA,EAAS;MACd,OAAO,IAAI;IACb;EAAC;AAAA;AAGH;AACA;AACA;AAFA,SAAAlL,kBAldoBlB,IAAc,EAAE;EAChC,IAAI,CAAC,IAAI,CAAC/G,OAAO,CAACE,MAAM,IAAI6G,IAAI,CAACa,QAAQ,KAAK,WAAW,EAAE;IACzD,OAAO,IAAI;EACb;;EAEA;EACA,IAAIb,IAAI,CAACa,QAAQ,KAAK,QAAQ,IAAIb,IAAI,CAACzE,KAAK,KAAK,kBAAkB,EAAE;IACnE,OAAO,IAAI;EACb;;EAEA;EACA,IAAIyE,IAAI,CAACzE,KAAK,KAAK,gBAAgB,IAAI,IAAA8Q,kCAA2B,EAACrM,IAAI,EAAE,gBAAgB,CAAC,EAAE;IAC1F,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAqcK,IAAMsM,MAAM,GAAAzT,OAAA,CAAAyT,MAAA,GAAGC,iCAAyB;;AAE/C;AACA;AACA;AAFA,IAAAC,QAAA,GAAA3T,OAAA,cAKeD,cAAc", "ignoreList": []}