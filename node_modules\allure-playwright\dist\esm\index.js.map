{"version": 3, "file": "index.js", "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_createForOfIteratorHelper", "Array", "isArray", "_unsupportedIterableToArray", "_n", "F", "s", "next", "_toArray", "_arrayWithHoles", "_iterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_nonIterableSpread", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_classPrivateMethodInitSpec", "_checkPrivateRedeclaration", "add", "has", "_toPrimitive", "_typeof", "toPrimitive", "String", "Number", "_assert<PERSON>lassBrand", "existsSync", "path", "process", "ContentType", "LabelName", "LinkType", "Stage", "Status", "extractMetadataFromString", "getMessageAndTraceFromError", "getMetadataLabel", "<PERSON><PERSON><PERSON><PERSON>", "stripAnsi", "ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE", "ReporterRuntime", "ShallowStepsStack", "createDefaultWriter", "createStepResult", "escapeRegExp", "formatLink", "getEnvironmentLabels", "getFrameworkLabel", "getHostLabel", "getLanguageLabel", "getPackageLabel", "getThreadLabel", "getWorstTestStepResult", "md5", "parseTestPlan", "randomUuid", "readImageAsBase64", "allurePlaywrightLegacyApi", "AFTER_HOOKS_ROOT_STEP_TITLE", "BEFORE_HOOKS_ROOT_STEP_TITLE", "diffEndRegexp", "isAfterHookStep", "isBeforeHookStep", "isDescendantOfStepWithTitle", "normalizeHookTitle", "statusToAllureStats", "_AllureReporter_brand", "WeakSet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "Date", "Map", "options", "suiteTitle", "detail", "onConfigure", "testPlan", "configElement", "testsWithSelectors", "tests", "selector", "v1ReporterTests", "v2ReporterTests", "cli<PERSON><PERSON>s", "selectorToGrepPattern", "normalize", "concat", "v2SelectorsArgs", "map", "replace", "v1SelectorsArgs", "split", "onError", "onExit", "onStdErr", "onStdOut", "onBegin", "suite", "allureRuntime", "writer", "resultsDir", "onTestBegin", "parent", "titleMetadata", "title", "project", "testFile<PERSON><PERSON>", "relative", "testDir", "location", "file", "relativeFileParts", "sep", "relativeFile", "join", "_suite$titlePath", "titlePath", "_suite$titlePath2", "suiteTitles", "nameSuites", "testCaseIdBase", "result", "cleanTitle", "labels", "links", "parameters", "steps", "testCaseId", "fullName", "line", "column", "_ref", "tags", "tag", "TAG", "startsWith", "substring", "_iterator", "annotations", "_step", "annotation", "type", "_this$options$links", "ISSUE", "url", "description", "_this$options$links2", "TMS", "annotationLabel", "status", "PASSED", "stage", "FINISHED", "attachments", "statusDetails", "err", "repeatEach", "repeatEachIndex", "testUuid", "startTest", "allureResultsUuids", "set", "id", "startedTestCasesTitlesCache", "onStepBegin", "_result", "step", "isRootBeforeHook", "isRootAfterHook", "isRootHook", "isBeforeHookDescendant", "isAfterHookDescendant", "isHookStep", "get", "includes", "category", "_this$allureRuntime", "_this$attachmentSteps", "currentStep", "attachmentSteps", "_shouldIgnoreStep", "baseStep", "start", "startTime", "getTime", "RUNNING", "uuid", "stack", "beforeHooksStepsStack", "afterHooksStepsStack", "startStep", "attachStack", "beforeHooksAttachmentsStack", "afterHooksAttachmentsStack", "updateStep", "step<PERSON><PERSON><PERSON>", "_attachStack$get", "stopStep", "undefined", "onStepEnd", "isAfterHook", "isHook", "_getWorstTestStepResu", "_ref2", "_ref2$status", "error", "FAILED", "duration", "_getWorstTestStepResu2", "_ref3", "_ref3$status", "_onTestEnd", "_callee", "_this", "_this$beforeHooksAtta", "_this$afterHooksAttac", "_this$attachmentSteps2", "threadId", "thread", "_test$parent$titlePat", "_test$parent$titlePat2", "projectSuiteTitle", "fileSuiteTitle", "beforeHooksStack", "afterHooksStack", "attachmentsInBeforeHooks", "attachmentsInAfterHooks", "attachmentToStepMap", "attachmentIndex", "_iterator2", "_step2", "_hookStep", "_iterator3", "_step3", "stepUuid", "_iterator4", "_step4", "_hookStep2", "attachment", "stepInfo", "_i", "_attachment", "_stepInfo", "hookStep", "isBeforeHook", "targetStack", "fileName", "_context", "parallelIndex", "workerIndex", "pid", "updateTest", "testResult", "PARENT_SUITE", "SUITE", "SUB_SUITE", "_test$annotations", "skipR<PERSON>on", "find", "message", "expectedStatus", "processAttachment", "stdout", "writeAttachment", "<PERSON><PERSON><PERSON>", "contentType", "TEXT", "stderr", "findStepByUuid", "addAttachment", "source", "mappedLabels", "reduce", "acc", "label", "<PERSON><PERSON><PERSON><PERSON>", "flatMap", "labelName", "labelsGroup", "_testResult$steps", "unshift", "_testResult$steps2", "stopTest", "writeTest", "onTestEnd", "_x", "_x2", "_addSkippedResults", "_callee2", "_this2", "unprocessedCases", "_iterator5", "_step5", "testCase", "_t", "_context2", "allTests", "_ref4", "SKIPPED", "errors", "retry", "globalStartTime", "addSkippedResults", "_onEnd", "_callee3", "_context3", "writeEnvironmentInfo", "writeCategoriesDefinitions", "onEnd", "printsToStdio", "processStepMetadataMessage", "attachmentStepUuid", "_message$data", "data", "_message$data$paramet", "_step$parameters", "_processAttachment", "_callee4", "allureRuntimeMessage", "parentUuid", "pathWithoutEnd", "actualBase64", "expectedBase64", "diffBase64", "diffName", "_context4", "body", "JSON", "parse", "applyRuntimeMessages", "match", "processedDiffs", "stringify", "expected", "actual", "diff", "IMAGEDIFF", "fileExtension", "_x3", "_x4", "_x5", "version", "allure", "expect"], "sources": ["../../src/index.ts"], "sourcesContent": ["/* eslint max-lines: off */\nimport type { FullConfig } from \"@playwright/test\";\nimport type { TestResult as PlaywrightTestResult, Suite, TestCase, TestStep } from \"@playwright/test/reporter\";\nimport { existsSync } from \"node:fs\";\nimport path from \"node:path\";\nimport process from \"node:process\";\nimport {\n  ContentType,\n  type ImageDiffAttachment,\n  type Label,\n  LabelName,\n  LinkType,\n  Stage,\n  Status,\n  type StepResult,\n  type TestResult,\n} from \"allure-js-commons\";\nimport type { RuntimeMessage, RuntimeStepMetadataMessage, TestPlanV1Test } from \"allure-js-commons/sdk\";\nimport {\n  extractMetadataFromString,\n  getMessageAndTraceFromError,\n  getMetadataLabel,\n  hasLabel,\n  stripAnsi,\n} from \"allure-js-commons/sdk\";\nimport {\n  ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE,\n  ReporterRuntime,\n  ShallowStepsStack,\n  createDefaultWriter,\n  createStepResult,\n  escapeRegExp,\n  formatLink,\n  getEnvironmentLabels,\n  getFrameworkLabel,\n  getHostLabel,\n  getLanguageLabel,\n  getPackageLabel,\n  getThreadLabel,\n  getWorstTestStepResult,\n  md5,\n  parseTestPlan,\n  randomUuid,\n  readImageAsBase64,\n} from \"allure-js-commons/sdk/reporter\";\nimport { allurePlaywrightLegacyApi } from \"./legacy.js\";\nimport type { AllurePlaywrightReporterConfig, AttachStack, ReporterV2 } from \"./model.js\";\nimport {\n  AFTER_HOOKS_ROOT_STEP_TITLE,\n  BEFORE_HOOKS_ROOT_STEP_TITLE,\n  diffEndRegexp,\n  isAfterHookStep,\n  isBeforeHookStep,\n  isDescendantOfStepWithTitle,\n  normalizeHookTitle,\n  statusToAllureStats,\n} from \"./utils.js\";\n\nexport class AllureReporter implements ReporterV2 {\n  config!: FullConfig;\n  suite!: Suite;\n  options: AllurePlaywrightReporterConfig;\n\n  private allureRuntime: ReporterRuntime | undefined;\n  private globalStartTime = new Date();\n  private processedDiffs: string[] = [];\n  private readonly startedTestCasesTitlesCache: string[] = [];\n  private readonly allureResultsUuids: Map<string, string> = new Map();\n  private readonly attachmentSteps: Map<string, (string | undefined)[]> = new Map();\n  private beforeHooksStepsStack: Map<string, ShallowStepsStack> = new Map();\n  private afterHooksStepsStack: Map<string, ShallowStepsStack> = new Map();\n  private beforeHooksAttachmentsStack: Map<string, AttachStack[]> = new Map();\n  private afterHooksAttachmentsStack: Map<string, AttachStack[]> = new Map();\n\n  constructor(config: AllurePlaywrightReporterConfig) {\n    this.options = { suiteTitle: true, detail: true, ...config };\n  }\n\n  onConfigure(config: FullConfig): void {\n    this.config = config;\n\n    const testPlan = parseTestPlan();\n\n    if (!testPlan) {\n      return;\n    }\n\n    // @ts-ignore\n    const configElement = config[Object.getOwnPropertySymbols(config)[0]];\n\n    if (!configElement) {\n      return;\n    }\n\n    const testsWithSelectors = testPlan.tests.filter((test) => test.selector);\n    const v1ReporterTests: TestPlanV1Test[] = [];\n    const v2ReporterTests: TestPlanV1Test[] = [];\n    const cliArgs: string[] = [];\n\n    testsWithSelectors.forEach((test) => {\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      if (!/#/.test(test.selector!)) {\n        v2ReporterTests.push(test);\n        return;\n      }\n\n      v1ReporterTests.push(test);\n    });\n\n    // The path needs to be specific to the current OS. Otherwise, it may not match against the test file.\n    const selectorToGrepPattern = (selector: string) => escapeRegExp(path.normalize(`/${selector}`));\n\n    if (v2ReporterTests.length) {\n      // we need to cut off column because playwright works only with line number\n      const v2SelectorsArgs = v2ReporterTests\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        .map((test) => test.selector!.replace(/:\\d+$/, \"\"))\n        .map(selectorToGrepPattern);\n\n      cliArgs.push(...v2SelectorsArgs);\n    }\n\n    if (v1ReporterTests.length) {\n      const v1SelectorsArgs = v1ReporterTests\n        // we can filter tests only by absolute path, so we need to cut off test name\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        .map((test) => test.selector!.split(\"#\")[0])\n        .map(selectorToGrepPattern);\n\n      cliArgs.push(...v1SelectorsArgs);\n    }\n\n    if (!cliArgs.length) {\n      return;\n    }\n\n    configElement.cliArgs = cliArgs;\n  }\n\n  onError(): void {}\n\n  onExit(): void {}\n\n  onStdErr(): void {}\n\n  onStdOut(): void {}\n\n  onBegin(suite: Suite): void {\n    this.suite = suite;\n    this.allureRuntime = new ReporterRuntime({\n      ...this.options,\n      writer: createDefaultWriter({ resultsDir: this.options.resultsDir }),\n    });\n  }\n\n  onTestBegin(test: TestCase) {\n    const suite = test.parent;\n    const titleMetadata = extractMetadataFromString(test.title);\n    const project = suite.project()!;\n    const testFilePath = path.relative(project?.testDir, test.location.file);\n    const relativeFileParts = testFilePath.split(path.sep);\n    const relativeFile = relativeFileParts.join(\"/\");\n    // root > project > file path > test.describe...\n    const [, , , ...suiteTitles] = suite.titlePath();\n    const nameSuites = suiteTitles.length > 0 ? `${suiteTitles.join(\" \")} ` : \"\";\n    const testCaseIdBase = `${relativeFile}#${nameSuites}${test.title}`;\n    const result: Partial<TestResult> = {\n      name: titleMetadata.cleanTitle,\n      labels: [...titleMetadata.labels, ...getEnvironmentLabels()],\n      links: [...titleMetadata.links],\n      parameters: [],\n      steps: [],\n      testCaseId: md5(testCaseIdBase),\n      fullName: `${relativeFile}:${test.location.line}:${test.location.column}`,\n      titlePath: relativeFileParts.concat(...suiteTitles),\n    };\n\n    result.labels!.push(getLanguageLabel());\n    result.labels!.push(getFrameworkLabel(\"playwright\"));\n    result.labels!.push(getPackageLabel(testFilePath));\n    result.labels!.push({ name: \"titlePath\", value: suite.titlePath().join(\" > \") });\n\n    // support for earlier playwright versions\n    if (\"tags\" in test) {\n      const tags: Label[] = test.tags.map((tag) => ({\n        name: LabelName.TAG,\n        value: tag.startsWith(\"@\") ? tag.substring(1) : tag,\n      }));\n      result.labels!.push(...tags);\n    }\n\n    if (\"annotations\" in test) {\n      for (const annotation of test.annotations) {\n        if (annotation.type === \"skip\" || annotation.type === \"fixme\") {\n          continue;\n        }\n\n        if (annotation.type === \"issue\") {\n          result.links!.push(\n            formatLink(this.options.links ?? {}, {\n              type: LinkType.ISSUE,\n              url: annotation.description!,\n            }),\n          );\n          continue;\n        }\n\n        if (annotation.type === \"tms\" || annotation.type === \"test_key\") {\n          result.links!.push(\n            formatLink(this.options.links ?? {}, {\n              type: LinkType.TMS,\n              url: annotation.description!,\n            }),\n          );\n          continue;\n        }\n\n        if (annotation.type === \"description\") {\n          result.description = annotation.description;\n          continue;\n        }\n\n        const annotationLabel = getMetadataLabel(annotation.type, annotation.description);\n\n        if (annotationLabel) {\n          result.labels!.push(annotationLabel);\n          continue;\n        }\n\n        result.steps!.push({\n          name: `${annotation.type}: ${annotation.description!}`,\n          status: Status.PASSED,\n          stage: Stage.FINISHED,\n          parameters: [],\n          steps: [],\n          attachments: [],\n          statusDetails: {},\n        });\n      }\n    }\n\n    if (project?.name) {\n      result.parameters!.push({ name: \"Project\", value: project.name });\n    }\n\n    if (project?.repeatEach > 1) {\n      result.parameters!.push({ name: \"Repetition\", value: `${test.repeatEachIndex + 1}` });\n    }\n\n    const testUuid = this.allureRuntime!.startTest(result);\n\n    this.allureResultsUuids.set(test.id, testUuid);\n    this.startedTestCasesTitlesCache.push(titleMetadata.cleanTitle);\n  }\n\n  #shouldIgnoreStep(step: TestStep) {\n    if (!this.options.detail && step.category !== \"test.step\") {\n      return true;\n    }\n\n    // ignore noisy route.continue()\n    if (step.category === \"pw:api\" && step.title === \"route.continue()\") {\n      return true;\n    }\n\n    // playwright doesn't report this step\n    if (step.title === \"Worker Cleanup\" || isDescendantOfStepWithTitle(step, \"Worker Cleanup\")) {\n      return true;\n    }\n\n    return false;\n  }\n\n  onStepBegin(test: TestCase, _result: PlaywrightTestResult, step: TestStep): void {\n    const isRootBeforeHook = step.title === BEFORE_HOOKS_ROOT_STEP_TITLE;\n    const isRootAfterHook = step.title === AFTER_HOOKS_ROOT_STEP_TITLE;\n    const isRootHook = isRootBeforeHook || isRootAfterHook;\n    const isBeforeHookDescendant = isBeforeHookStep(step);\n    const isAfterHookDescendant = isAfterHookStep(step);\n    const isHookStep = isBeforeHookDescendant || isAfterHookDescendant;\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n\n    if ([\"test.attach\", \"attach\"].includes(step.category) && !isHookStep) {\n      const currentStep = this.allureRuntime?.currentStep(testUuid);\n      this.attachmentSteps.set(testUuid, [...(this.attachmentSteps.get(testUuid) ?? []), currentStep]);\n      return;\n    }\n\n    if (this.#shouldIgnoreStep(step)) {\n      return;\n    }\n\n    const baseStep: StepResult = {\n      ...createStepResult(),\n      name: step.title,\n      start: step.startTime.getTime(),\n      stage: Stage.RUNNING,\n      uuid: randomUuid(),\n    };\n\n    if (isHookStep) {\n      const stack = isBeforeHookDescendant\n        ? this.beforeHooksStepsStack.get(test.id)!\n        : this.afterHooksStepsStack.get(test.id)!;\n\n      if ([\"test.attach\", \"attach\"].includes(step.category)) {\n        stack.startStep(baseStep);\n\n        const attachStack = isBeforeHookDescendant ? this.beforeHooksAttachmentsStack : this.afterHooksAttachmentsStack;\n\n        stack.updateStep((stepResult) => {\n          stepResult.name = normalizeHookTitle(stepResult.name!);\n          stepResult.stage = Stage.FINISHED;\n          attachStack.set(test.id, [...(attachStack.get(test.id) ?? []), { ...step, uuid: stepResult.uuid as string }]);\n        });\n        stack.stopStep();\n        return;\n      }\n      stack.startStep(baseStep);\n\n      return;\n    }\n\n    if (isRootHook) {\n      const stack = new ShallowStepsStack();\n      stack.startStep(baseStep);\n      if (isRootBeforeHook) {\n        this.beforeHooksStepsStack.set(test.id, stack);\n      } else {\n        this.afterHooksStepsStack.set(test.id, stack);\n      }\n      return;\n    }\n\n    this.allureRuntime!.startStep(testUuid, undefined, baseStep)!;\n  }\n\n  onStepEnd(test: TestCase, _result: PlaywrightTestResult, step: TestStep): void {\n    if (this.#shouldIgnoreStep(step)) {\n      return;\n    }\n    // ignore test.attach steps since attachments are already in the report\n    if ([\"test.attach\", \"attach\"].includes(step.category)) {\n      return;\n    }\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n    const isRootBeforeHook = step.title === BEFORE_HOOKS_ROOT_STEP_TITLE;\n    const isRootAfterHook = step.title === AFTER_HOOKS_ROOT_STEP_TITLE;\n    const isBeforeHookDescendant = isBeforeHookStep(step);\n    const isAfterHookDescendant = isAfterHookStep(step);\n    const isAfterHook = isRootAfterHook || isAfterHookDescendant;\n    const isHook = isRootBeforeHook || isRootAfterHook || isBeforeHookDescendant || isAfterHookDescendant;\n\n    if (isHook) {\n      const stack = isAfterHook ? this.afterHooksStepsStack.get(test.id)! : this.beforeHooksStepsStack.get(test.id)!;\n\n      stack.updateStep((stepResult) => {\n        const { status = Status.PASSED } = getWorstTestStepResult(stepResult.steps) ?? {};\n        stepResult.status = step.error ? Status.FAILED : status;\n        stepResult.stage = Stage.FINISHED;\n        if (step.error) {\n          stepResult.statusDetails = { ...getMessageAndTraceFromError(step.error) };\n        }\n      });\n      stack.stopStep({\n        duration: step.duration,\n      });\n      return;\n    }\n\n    const currentStep = this.allureRuntime!.currentStep(testUuid);\n\n    if (!currentStep) {\n      return;\n    }\n\n    this.allureRuntime!.updateStep(currentStep, (stepResult) => {\n      const { status = Status.PASSED } = getWorstTestStepResult(stepResult.steps) ?? {};\n      stepResult.status = step.error ? Status.FAILED : status;\n      stepResult.stage = Stage.FINISHED;\n      if (step.error) {\n        stepResult.statusDetails = { ...getMessageAndTraceFromError(step.error) };\n      }\n    });\n    this.allureRuntime!.stopStep(currentStep, { duration: step.duration });\n  }\n\n  async onTestEnd(test: TestCase, result: PlaywrightTestResult) {\n    const testUuid = this.allureResultsUuids.get(test.id)!;\n    // We need to check parallelIndex first because pw introduced this field only in v1.30.0\n    const threadId = result.parallelIndex !== undefined ? result.parallelIndex : result.workerIndex;\n    const thread = `pid-${process.pid}-worker-${threadId}`;\n    const error = result.error;\n    // only apply default suites if not set by user\n    const [, projectSuiteTitle, fileSuiteTitle, ...suiteTitles] = test.parent.titlePath();\n    const beforeHooksStack = this.beforeHooksStepsStack.get(test.id);\n    const afterHooksStack = this.afterHooksStepsStack.get(test.id);\n\n    this.allureRuntime!.updateTest(testUuid, (testResult) => {\n      testResult.labels.push(getHostLabel());\n      testResult.labels.push(getThreadLabel(thread));\n\n      if (projectSuiteTitle && !hasLabel(testResult, LabelName.PARENT_SUITE)) {\n        testResult.labels.push({ name: LabelName.PARENT_SUITE, value: projectSuiteTitle });\n      }\n\n      if (this.options.suiteTitle && fileSuiteTitle && !hasLabel(testResult, LabelName.SUITE)) {\n        testResult.labels.push({ name: LabelName.SUITE, value: fileSuiteTitle });\n      }\n\n      if (suiteTitles.length > 0 && !hasLabel(testResult, LabelName.SUB_SUITE)) {\n        testResult.labels.push({ name: LabelName.SUB_SUITE, value: suiteTitles.join(\" > \") });\n      }\n\n      if (error) {\n        testResult.statusDetails = { ...getMessageAndTraceFromError(error) };\n      } else {\n        const skipReason = test.annotations?.find(\n          (annotation) => annotation.type === \"skip\" || annotation.type === \"fixme\",\n        )?.description;\n\n        if (skipReason) {\n          testResult.statusDetails = { ...testResult.statusDetails, message: skipReason };\n        }\n      }\n\n      testResult.status = statusToAllureStats(result.status, test.expectedStatus);\n      testResult.stage = Stage.FINISHED;\n    });\n\n    const attachmentsInBeforeHooks = this.beforeHooksAttachmentsStack.get(test.id) ?? [];\n    const attachmentsInAfterHooks = this.afterHooksAttachmentsStack.get(test.id) ?? [];\n    const attachmentSteps = this.attachmentSteps.get(testUuid) ?? [];\n\n    const attachmentToStepMap = new Map<number, { stepUuid?: string; isHook: boolean; hookStep?: AttachStack }>();\n\n    let attachmentIndex = 0;\n\n    for (const hookStep of attachmentsInBeforeHooks) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid: hookStep.uuid,\n        isHook: true,\n        hookStep,\n      });\n      attachmentIndex++;\n    }\n\n    for (const stepUuid of attachmentSteps) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid,\n        isHook: false,\n      });\n      attachmentIndex++;\n    }\n\n    for (const hookStep of attachmentsInAfterHooks) {\n      attachmentToStepMap.set(attachmentIndex, {\n        stepUuid: hookStep.uuid,\n        isHook: true,\n        hookStep,\n      });\n      attachmentIndex++;\n    }\n\n    for (let i = 0; i < result.attachments.length; i++) {\n      const attachment = result.attachments[i];\n      const stepInfo = attachmentToStepMap.get(i);\n\n      if (stepInfo?.isHook) {\n        continue;\n      } else if (stepInfo?.stepUuid) {\n        await this.processAttachment(testUuid, stepInfo.stepUuid, attachment);\n      } else {\n        await this.processAttachment(testUuid, undefined, attachment);\n      }\n    }\n\n    if (result.stdout.length > 0) {\n      this.allureRuntime!.writeAttachment(\n        testUuid,\n        undefined,\n        \"stdout\",\n        Buffer.from(stripAnsi(result.stdout.join(\"\")), \"utf-8\"),\n        {\n          contentType: ContentType.TEXT,\n        },\n      );\n    }\n\n    if (result.stderr.length > 0) {\n      this.allureRuntime!.writeAttachment(\n        testUuid,\n        undefined,\n        \"stderr\",\n        Buffer.from(stripAnsi(result.stderr.join(\"\")), \"utf-8\"),\n        {\n          contentType: ContentType.TEXT,\n        },\n      );\n    }\n\n    // FIXME: temp logic for labels override, we need it here to keep the reporter compatible with v2 API\n    // in next iterations we need to implement the logic for every javascript integration\n\n    for (let i = 0; i < result.attachments.length; i++) {\n      const attachment = result.attachments[i];\n      const stepInfo = attachmentToStepMap.get(i);\n\n      if (stepInfo?.isHook && stepInfo.hookStep) {\n        const hookStep = stepInfo.hookStep;\n        const isBeforeHook = attachmentsInBeforeHooks.includes(hookStep);\n        const targetStack = isBeforeHook ? beforeHooksStack : afterHooksStack;\n\n        if (attachment.contentType === ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE) {\n          await this.processAttachment(testUuid, hookStep.uuid, attachment);\n          continue;\n        }\n\n        if (targetStack) {\n          const stepResult = targetStack.findStepByUuid(hookStep.uuid);\n          if (stepResult) {\n            const fileName = targetStack.addAttachment(attachment, this.allureRuntime!.writer);\n            stepResult.attachments.push({\n              name: attachment.name,\n              type: attachment.contentType,\n              source: fileName,\n            });\n          }\n        }\n      }\n    }\n\n    this.allureRuntime!.updateTest(testUuid, (testResult) => {\n      const mappedLabels = testResult.labels.reduce<Record<string, Label[]>>((acc, label) => {\n        if (!acc[label.name]) {\n          acc[label.name] = [];\n        }\n\n        acc[label.name].push(label);\n\n        return acc;\n      }, {});\n      const newLabels = Object.keys(mappedLabels).flatMap((labelName) => {\n        const labelsGroup = mappedLabels[labelName];\n\n        if (\n          labelName === LabelName.SUITE ||\n          labelName === LabelName.PARENT_SUITE ||\n          labelName === LabelName.SUB_SUITE\n        ) {\n          return labelsGroup.slice(-1);\n        }\n\n        return labelsGroup;\n      });\n\n      if (beforeHooksStack) {\n        testResult.steps.unshift(...beforeHooksStack.steps);\n        this.beforeHooksStepsStack.delete(test.id);\n      }\n\n      if (afterHooksStack) {\n        testResult.steps.push(...afterHooksStack.steps);\n        this.afterHooksStepsStack.delete(test.id);\n      }\n\n      testResult.labels = newLabels;\n    });\n    this.allureRuntime!.stopTest(testUuid, { duration: result.duration });\n    this.allureRuntime!.writeTest(testUuid);\n  }\n\n  async addSkippedResults() {\n    const unprocessedCases = this.suite.allTests().filter(({ title }) => {\n      const titleMetadata = extractMetadataFromString(title);\n\n      return !this.startedTestCasesTitlesCache.includes(titleMetadata.cleanTitle);\n    });\n\n    for (const testCase of unprocessedCases) {\n      this.onTestBegin(testCase);\n      await this.onTestEnd(testCase, {\n        status: Status.SKIPPED,\n        attachments: [],\n        duration: 0,\n        errors: [],\n        parallelIndex: 0,\n        workerIndex: 0,\n        retry: 0,\n        steps: [],\n        stderr: [],\n        stdout: [],\n        startTime: this.globalStartTime,\n        annotations: [],\n      });\n    }\n  }\n\n  async onEnd() {\n    await this.addSkippedResults();\n\n    this.allureRuntime!.writeEnvironmentInfo();\n    this.allureRuntime!.writeCategoriesDefinitions();\n  }\n\n  printsToStdio(): boolean {\n    return false;\n  }\n\n  private processStepMetadataMessage(attachmentStepUuid: string, message: RuntimeStepMetadataMessage) {\n    const { name, parameters = [] } = message.data;\n\n    this.allureRuntime!.updateStep(attachmentStepUuid, (step) => {\n      if (name) {\n        step.name = name;\n      }\n\n      step.parameters.push(...parameters);\n    });\n  }\n\n  private async processAttachment(\n    testUuid: string,\n    attachmentStepUuid: string | undefined,\n    attachment: {\n      name: string;\n      contentType: string;\n      path?: string;\n      body?: Buffer;\n    },\n  ) {\n    if (!attachment.body && !attachment.path) {\n      return;\n    }\n\n    const allureRuntimeMessage = attachment.contentType === ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE;\n\n    if (allureRuntimeMessage && !attachment.body) {\n      return;\n    }\n\n    if (allureRuntimeMessage) {\n      const message = JSON.parse(attachment.body!.toString()) as RuntimeMessage;\n\n      if (message.type === \"step_metadata\") {\n        this.processStepMetadataMessage(attachmentStepUuid!, message);\n        return;\n      }\n\n      this.allureRuntime!.applyRuntimeMessages(testUuid, [message]);\n      return;\n    }\n\n    const parentUuid = this.allureRuntime!.startStep(testUuid, attachmentStepUuid, { name: attachment.name });\n\n    // only stop if step is created. Step may not be created only if test with specified uuid doesn't exists.\n    // usually, missing test by uuid means we should completely skip result processing;\n    // the later operations are safe and will only produce console warnings\n    if (parentUuid) {\n      this.allureRuntime!.stopStep(parentUuid, undefined);\n    }\n\n    if (attachment.body) {\n      this.allureRuntime!.writeAttachment(testUuid, parentUuid, attachment.name, attachment.body, {\n        contentType: attachment.contentType,\n      });\n    } else if (!existsSync(attachment.path!)) {\n      return;\n    } else {\n      const contentType =\n        attachment.name === \"trace\" && attachment.contentType === \"application/zip\"\n          ? \"application/vnd.allure.playwright-trace\"\n          : attachment.contentType;\n\n      this.allureRuntime!.writeAttachment(testUuid, parentUuid, attachment.name, attachment.path!, {\n        contentType,\n      });\n    }\n\n    if (!attachment.name.match(diffEndRegexp)) {\n      return;\n    }\n\n    const pathWithoutEnd = attachment.path!.replace(diffEndRegexp, \"\");\n\n    if (this.processedDiffs.includes(pathWithoutEnd)) {\n      return;\n    }\n\n    const actualBase64 = await readImageAsBase64(`${pathWithoutEnd}-actual.png`);\n    const expectedBase64 = await readImageAsBase64(`${pathWithoutEnd}-expected.png`);\n    const diffBase64 = await readImageAsBase64(`${pathWithoutEnd}-diff.png`);\n    const diffName = attachment.name.replace(diffEndRegexp, \"\");\n\n    this.allureRuntime!.writeAttachment(\n      testUuid,\n      undefined,\n      diffName,\n      Buffer.from(\n        JSON.stringify({\n          expected: expectedBase64,\n          actual: actualBase64,\n          diff: diffBase64,\n          name: diffName,\n        } as ImageDiffAttachment),\n        \"utf-8\",\n      ),\n      {\n        contentType: ContentType.IMAGEDIFF,\n        fileExtension: \".imagediff\",\n      },\n    );\n\n    this.processedDiffs.push(pathWithoutEnd);\n  }\n\n  version(): \"v2\" {\n    return \"v2\";\n  }\n}\n\n/**\n * @deprecated for removal, import functions directly from \"allure-js-commons\".\n */\nexport const allure = allurePlaywrightLegacyApi;\n\n/**\n * @deprecated for removal, import functions directly from \"@playwright/test\".\n */\nexport { test, expect } from \"@playwright/test\";\n\nexport default AllureReporter;\n"], "mappings": ";0BACA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,eAAAP,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAmB,kBAAA,cAAAC,2BAAA,KAAA9B,CAAA,GAAAY,MAAA,CAAAmB,cAAA,MAAAvB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAmB,0BAAA,CAAArB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAoB,cAAA,GAAApB,MAAA,CAAAoB,cAAA,CAAAjC,CAAA,EAAA+B,0BAAA,KAAA/B,CAAA,CAAAkC,SAAA,GAAAH,0BAAA,EAAAhB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA8B,iBAAA,CAAApB,SAAA,GAAAqB,0BAAA,EAAAhB,mBAAA,CAAAH,CAAA,iBAAAmB,0BAAA,GAAAhB,mBAAA,CAAAgB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAApB,mBAAA,CAAAgB,0BAAA,EAAAzB,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAwB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA7B,CAAA,EAAA8B,CAAA,EAAAtB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA0B,cAAA,QAAA/B,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAAyB,mBAAAxC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAqC,UAAA,GAAAxC,CAAA,EAAAyC,YAAA,GAAAzC,CAAA,EAAA0C,QAAA,GAAA1C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA4C,OAAA,CAAA1C,CAAA,EAAAE,CAAA,EAAAJ,CAAA,UAAAM,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA4C,mBAAAzC,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAkC,OAAA,CAAAC,OAAA,CAAAnC,CAAA,EAAAoC,IAAA,CAAA9C,CAAA,EAAAI,CAAA;AAAA,SAAA2C,kBAAA7C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAkD,SAAA,aAAAJ,OAAA,WAAA5C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAA+C,KAAA,CAAAlD,CAAA,EAAAD,CAAA,YAAAoD,MAAAhD,CAAA,IAAAyC,kBAAA,CAAAxB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA8C,KAAA,EAAAC,MAAA,UAAAjD,CAAA,cAAAiD,OAAAjD,CAAA,IAAAyC,kBAAA,CAAAxB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA8C,KAAA,EAAAC,MAAA,WAAAjD,CAAA,KAAAgD,KAAA;AAAA,SAAAE,2BAAApD,CAAA,EAAAF,CAAA,QAAAC,CAAA,yBAAAE,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,KAAAH,CAAA,qBAAAD,CAAA,QAAAsD,KAAA,CAAAC,OAAA,CAAAtD,CAAA,MAAAD,CAAA,GAAAwD,2BAAA,CAAAvD,CAAA,MAAAF,CAAA,IAAAE,CAAA,uBAAAA,CAAA,CAAAsB,MAAA,IAAAvB,CAAA,KAAAC,CAAA,GAAAD,CAAA,OAAAyD,EAAA,MAAAC,CAAA,YAAAA,EAAA,eAAAC,CAAA,EAAAD,CAAA,EAAAvD,CAAA,WAAAA,EAAA,WAAAsD,EAAA,IAAAxD,CAAA,CAAAsB,MAAA,KAAAI,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAA3B,CAAA,CAAAwD,EAAA,UAAA1D,CAAA,WAAAA,EAAAE,CAAA,UAAAA,CAAA,KAAAc,CAAA,EAAA2C,CAAA,gBAAAjC,SAAA,iJAAApB,CAAA,EAAAe,CAAA,OAAAT,CAAA,gBAAAgD,CAAA,WAAAA,EAAA,IAAA3D,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAzB,CAAA,MAAAE,CAAA,WAAAA,EAAA,QAAAF,CAAA,GAAAD,CAAA,CAAA4D,IAAA,WAAAxC,CAAA,GAAAnB,CAAA,CAAA0B,IAAA,EAAA1B,CAAA,KAAAF,CAAA,WAAAA,EAAAE,CAAA,IAAAU,CAAA,OAAAN,CAAA,GAAAJ,CAAA,KAAAc,CAAA,WAAAA,EAAA,UAAAK,CAAA,YAAApB,CAAA,cAAAA,CAAA,8BAAAW,CAAA,QAAAN,CAAA;AAAA,SAAAwD,SAAA5D,CAAA,WAAA6D,eAAA,CAAA7D,CAAA,KAAA8D,gBAAA,CAAA9D,CAAA,KAAAuD,2BAAA,CAAAvD,CAAA,KAAA+D,gBAAA;AAAA,SAAAA,iBAAA,cAAAvC,SAAA;AAAA,SAAAqC,gBAAA7D,CAAA,QAAAqD,KAAA,CAAAC,OAAA,CAAAtD,CAAA,UAAAA,CAAA;AAAA,SAAAgE,mBAAAhE,CAAA,WAAAiE,kBAAA,CAAAjE,CAAA,KAAA8D,gBAAA,CAAA9D,CAAA,KAAAuD,2BAAA,CAAAvD,CAAA,KAAAkE,kBAAA;AAAA,SAAAA,mBAAA,cAAA1C,SAAA;AAAA,SAAA+B,4BAAAvD,CAAA,EAAAmB,CAAA,QAAAnB,CAAA,2BAAAA,CAAA,SAAAmE,iBAAA,CAAAnE,CAAA,EAAAmB,CAAA,OAAApB,CAAA,MAAAqE,QAAA,CAAA3C,IAAA,CAAAzB,CAAA,EAAAqE,KAAA,6BAAAtE,CAAA,IAAAC,CAAA,CAAAsE,WAAA,KAAAvE,CAAA,GAAAC,CAAA,CAAAsE,WAAA,CAAAC,IAAA,aAAAxE,CAAA,cAAAA,CAAA,GAAAsD,KAAA,CAAAmB,IAAA,CAAAxE,CAAA,oBAAAD,CAAA,+CAAA0E,IAAA,CAAA1E,CAAA,IAAAoE,iBAAA,CAAAnE,CAAA,EAAAmB,CAAA;AAAA,SAAA2C,iBAAA9D,CAAA,8BAAAC,MAAA,YAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,aAAAH,CAAA,uBAAAqD,KAAA,CAAAmB,IAAA,CAAAxE,CAAA;AAAA,SAAAiE,mBAAAjE,CAAA,QAAAqD,KAAA,CAAAC,OAAA,CAAAtD,CAAA,UAAAmE,iBAAA,CAAAnE,CAAA;AAAA,SAAAmE,kBAAAnE,CAAA,EAAAmB,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,MAAAH,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,YAAAxB,CAAA,MAAAI,CAAA,GAAAmD,KAAA,CAAAlC,CAAA,GAAArB,CAAA,GAAAqB,CAAA,EAAArB,CAAA,IAAAI,CAAA,CAAAJ,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAI,CAAA;AAAA,SAAAwE,QAAA5E,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAY,MAAA,CAAAgE,IAAA,CAAA7E,CAAA,OAAAa,MAAA,CAAAiE,qBAAA,QAAAxE,CAAA,GAAAO,MAAA,CAAAiE,qBAAA,CAAA9E,CAAA,GAAAE,CAAA,KAAAI,CAAA,GAAAA,CAAA,CAAAyE,MAAA,WAAA7E,CAAA,WAAAW,MAAA,CAAAmE,wBAAA,CAAAhF,CAAA,EAAAE,CAAA,EAAAuC,UAAA,OAAAxC,CAAA,CAAAgF,IAAA,CAAA9B,KAAA,CAAAlD,CAAA,EAAAK,CAAA,YAAAL,CAAA;AAAA,SAAAiF,cAAAlF,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAgD,SAAA,CAAA1B,MAAA,EAAAtB,CAAA,UAAAD,CAAA,WAAAiD,SAAA,CAAAhD,CAAA,IAAAgD,SAAA,CAAAhD,CAAA,QAAAA,CAAA,OAAA0E,OAAA,CAAA/D,MAAA,CAAAZ,CAAA,OAAAkF,OAAA,WAAAjF,CAAA,IAAAkF,eAAA,CAAApF,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAW,MAAA,CAAAwE,yBAAA,GAAAxE,MAAA,CAAAyE,gBAAA,CAAAtF,CAAA,EAAAa,MAAA,CAAAwE,yBAAA,CAAApF,CAAA,KAAA2E,OAAA,CAAA/D,MAAA,CAAAZ,CAAA,GAAAkF,OAAA,WAAAjF,CAAA,IAAAW,MAAA,CAAA0B,cAAA,CAAAvC,CAAA,EAAAE,CAAA,EAAAW,MAAA,CAAAmE,wBAAA,CAAA/E,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAAuF,gBAAAlE,CAAA,EAAAjB,CAAA,UAAAiB,CAAA,YAAAjB,CAAA,aAAAsB,SAAA;AAAA,SAAA8D,kBAAAxF,CAAA,EAAAE,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAsB,MAAA,EAAAvB,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAAmC,UAAA,GAAAnC,CAAA,CAAAmC,UAAA,QAAAnC,CAAA,CAAAoC,YAAA,kBAAApC,CAAA,KAAAA,CAAA,CAAAqC,QAAA,QAAA9B,MAAA,CAAA0B,cAAA,CAAAvC,CAAA,EAAAyF,cAAA,CAAAnF,CAAA,CAAAoF,GAAA,GAAApF,CAAA;AAAA,SAAAqF,aAAA3F,CAAA,EAAAE,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAsF,iBAAA,CAAAxF,CAAA,CAAAU,SAAA,EAAAR,CAAA,GAAAD,CAAA,IAAAuF,iBAAA,CAAAxF,CAAA,EAAAC,CAAA,GAAAY,MAAA,CAAA0B,cAAA,CAAAvC,CAAA,iBAAA2C,QAAA,SAAA3C,CAAA;AAAA,SAAA4F,4BAAA5F,CAAA,EAAAqB,CAAA,IAAAwE,0BAAA,CAAA7F,CAAA,EAAAqB,CAAA,GAAAA,CAAA,CAAAyE,GAAA,CAAA9F,CAAA;AAAA,SAAA6F,2BAAA7F,CAAA,EAAAC,CAAA,QAAAA,CAAA,CAAA8F,GAAA,CAAA/F,CAAA,aAAA0B,SAAA;AAAA,SAAA0D,gBAAApF,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAuF,cAAA,CAAAvF,CAAA,MAAAF,CAAA,GAAAa,MAAA,CAAA0B,cAAA,CAAAvC,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAA5B,CAAA,EAAAwC,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAA3C,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAyF,eAAAxF,CAAA,QAAAO,CAAA,GAAAwF,YAAA,CAAA/F,CAAA,gCAAAgG,OAAA,CAAAzF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwF,aAAA/F,CAAA,EAAAC,CAAA,oBAAA+F,OAAA,CAAAhG,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAE,MAAA,CAAA+F,WAAA,kBAAAlG,CAAA,QAAAQ,CAAA,GAAAR,CAAA,CAAA2B,IAAA,CAAA1B,CAAA,EAAAC,CAAA,gCAAA+F,OAAA,CAAAzF,CAAA,UAAAA,CAAA,YAAAkB,SAAA,yEAAAxB,CAAA,GAAAiG,MAAA,GAAAC,MAAA,EAAAnG,CAAA;AAAA,SAAAoG,kBAAArG,CAAA,EAAAC,CAAA,EAAAG,CAAA,6BAAAJ,CAAA,GAAAA,CAAA,KAAAC,CAAA,GAAAD,CAAA,CAAA+F,GAAA,CAAA9F,CAAA,UAAAiD,SAAA,CAAA1B,MAAA,OAAAvB,CAAA,GAAAG,CAAA,YAAAsB,SAAA;AADA;;AAGA,SAAS4E,UAAU,QAAQ,SAAS;AACpC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,OAAO,MAAM,cAAc;AAClC,SACEC,WAAW,EAGXC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,QAGD,mBAAmB;AAE1B,SACEC,yBAAyB,EACzBC,2BAA2B,EAC3BC,gBAAgB,EAChBC,QAAQ,EACRC,SAAS,QACJ,uBAAuB;AAC9B,SACEC,mCAAmC,EACnCC,eAAe,EACfC,iBAAiB,EACjBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZC,UAAU,EACVC,oBAAoB,EACpBC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,GAAG,EACHC,aAAa,EACbC,UAAU,EACVC,iBAAiB,QACZ,gCAAgC;AACvC,SAASC,yBAAyB,QAAQ,aAAa;AAEvD,SACEC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,2BAA2B,EAC3BC,kBAAkB,EAClBC,mBAAmB,QACd,YAAY;AAAC,IAAAC,qBAAA,oBAAAC,OAAA;AAEpB,WAAaC,cAAc;EAgBzB,SAAAA,eAAYC,MAAsC,EAAE;IAAA1D,eAAA,OAAAyD,cAAA;IAAApD,2BAAA,OAAAkD,qBAAA;IAAA1D,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAV1B,IAAI8D,IAAI,CAAC,CAAC;IAAA9D,eAAA,yBACD,EAAE;IAAAA,eAAA,sCACoB,EAAE;IAAAA,eAAA,6BACA,IAAI+D,GAAG,CAAC,CAAC;IAAA/D,eAAA,0BACI,IAAI+D,GAAG,CAAC,CAAC;IAAA/D,eAAA,gCACjB,IAAI+D,GAAG,CAAC,CAAC;IAAA/D,eAAA,+BACV,IAAI+D,GAAG,CAAC,CAAC;IAAA/D,eAAA,sCACN,IAAI+D,GAAG,CAAC,CAAC;IAAA/D,eAAA,qCACV,IAAI+D,GAAG,CAAC,CAAC;IAGxE,IAAI,CAACC,OAAO,GAAAlE,aAAA;MAAKmE,UAAU,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,GAAKL,MAAM,CAAE;EAC9D;EAAC,OAAAtD,YAAA,CAAAqD,cAAA;IAAAtD,GAAA;IAAA7D,KAAA,EAED,SAAA0H,WAAWA,CAACN,MAAkB,EAAQ;MACpC,IAAI,CAACA,MAAM,GAAGA,MAAM;MAEpB,IAAMO,QAAQ,GAAGtB,aAAa,CAAC,CAAC;MAEhC,IAAI,CAACsB,QAAQ,EAAE;QACb;MACF;;MAEA;MACA,IAAMC,aAAa,GAAGR,MAAM,CAACpI,MAAM,CAACiE,qBAAqB,CAACmE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MAErE,IAAI,CAACQ,aAAa,EAAE;QAClB;MACF;MAEA,IAAMC,kBAAkB,GAAGF,QAAQ,CAACG,KAAK,CAAC5E,MAAM,CAAC,UAACJ,IAAI;QAAA,OAAKA,IAAI,CAACiF,QAAQ;MAAA,EAAC;MACzE,IAAMC,eAAiC,GAAG,EAAE;MAC5C,IAAMC,eAAiC,GAAG,EAAE;MAC5C,IAAMC,OAAiB,GAAG,EAAE;MAE5BL,kBAAkB,CAACvE,OAAO,CAAC,UAACR,IAAI,EAAK;QACnC;QACA,IAAI,CAAC,GAAG,CAACA,IAAI,CAACA,IAAI,CAACiF,QAAS,CAAC,EAAE;UAC7BE,eAAe,CAAC7E,IAAI,CAACN,IAAI,CAAC;UAC1B;QACF;QAEAkF,eAAe,CAAC5E,IAAI,CAACN,IAAI,CAAC;MAC5B,CAAC,CAAC;;MAEF;MACA,IAAMqF,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIJ,QAAgB;QAAA,OAAKpC,YAAY,CAACjB,IAAI,CAAC0D,SAAS,KAAAC,MAAA,CAAKN,QAAQ,CAAE,CAAC,CAAC;MAAA;MAEhG,IAAIE,eAAe,CAACtI,MAAM,EAAE;QAC1B;QACA,IAAM2I,eAAe,GAAGL;QACtB;QAAA,CACCM,GAAG,CAAC,UAACzF,IAAI;UAAA,OAAKA,IAAI,CAACiF,QAAQ,CAAES,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAAA,EAAC,CAClDD,GAAG,CAACJ,qBAAqB,CAAC;QAE7BD,OAAO,CAAC9E,IAAI,CAAA9B,KAAA,CAAZ4G,OAAO,EAAA7F,kBAAA,CAASiG,eAAe,EAAC;MAClC;MAEA,IAAIN,eAAe,CAACrI,MAAM,EAAE;QAC1B,IAAM8I,eAAe,GAAGT;QACtB;QACA;QAAA,CACCO,GAAG,CAAC,UAACzF,IAAI;UAAA,OAAKA,IAAI,CAACiF,QAAQ,CAAEW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAA,EAAC,CAC3CH,GAAG,CAACJ,qBAAqB,CAAC;QAE7BD,OAAO,CAAC9E,IAAI,CAAA9B,KAAA,CAAZ4G,OAAO,EAAA7F,kBAAA,CAASoG,eAAe,EAAC;MAClC;MAEA,IAAI,CAACP,OAAO,CAACvI,MAAM,EAAE;QACnB;MACF;MAEAiI,aAAa,CAACM,OAAO,GAAGA,OAAO;IACjC;EAAC;IAAArE,GAAA;IAAA7D,KAAA,EAED,SAAA2I,OAAOA,CAAA,EAAS,CAAC;EAAC;IAAA9E,GAAA;IAAA7D,KAAA,EAElB,SAAA4I,MAAMA,CAAA,EAAS,CAAC;EAAC;IAAA/E,GAAA;IAAA7D,KAAA,EAEjB,SAAA6I,QAAQA,CAAA,EAAS,CAAC;EAAC;IAAAhF,GAAA;IAAA7D,KAAA,EAEnB,SAAA8I,QAAQA,CAAA,EAAS,CAAC;EAAC;IAAAjF,GAAA;IAAA7D,KAAA,EAEnB,SAAA+I,OAAOA,CAACC,KAAY,EAAQ;MAC1B,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,aAAa,GAAG,IAAI1D,eAAe,CAAAlC,aAAA,CAAAA,aAAA,KACnC,IAAI,CAACkE,OAAO;QACf2B,MAAM,EAAEzD,mBAAmB,CAAC;UAAE0D,UAAU,EAAE,IAAI,CAAC5B,OAAO,CAAC4B;QAAW,CAAC;MAAC,EACrE,CAAC;IACJ;EAAC;IAAAtF,GAAA;IAAA7D,KAAA,EAED,SAAAoJ,WAAWA,CAACtG,IAAc,EAAE;MAC1B,IAAMkG,KAAK,GAAGlG,IAAI,CAACuG,MAAM;MACzB,IAAMC,aAAa,GAAGrE,yBAAyB,CAACnC,IAAI,CAACyG,KAAK,CAAC;MAC3D,IAAMC,OAAO,GAAGR,KAAK,CAACQ,OAAO,CAAC,CAAE;MAChC,IAAMC,YAAY,GAAG/E,IAAI,CAACgF,QAAQ,CAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,EAAE7G,IAAI,CAAC8G,QAAQ,CAACC,IAAI,CAAC;MACxE,IAAMC,iBAAiB,GAAGL,YAAY,CAACf,KAAK,CAAChE,IAAI,CAACqF,GAAG,CAAC;MACtD,IAAMC,YAAY,GAAGF,iBAAiB,CAACG,IAAI,CAAC,GAAG,CAAC;MAChD;MACA,IAAAC,gBAAA,GAA+BlB,KAAK,CAACmB,SAAS,CAAC,CAAC;QAAAC,iBAAA,GAAAnI,QAAA,CAAAiI,gBAAA;QAAhCG,WAAW,GAAAD,iBAAA,CAAA1H,KAAA;MAC3B,IAAM4H,UAAU,GAAGD,WAAW,CAAC1K,MAAM,GAAG,CAAC,MAAA0I,MAAA,CAAMgC,WAAW,CAACJ,IAAI,CAAC,GAAG,CAAC,SAAM,EAAE;MAC5E,IAAMM,cAAc,MAAAlC,MAAA,CAAM2B,YAAY,OAAA3B,MAAA,CAAIiC,UAAU,EAAAjC,MAAA,CAAGvF,IAAI,CAACyG,KAAK,CAAE;MACnE,IAAMiB,MAA2B,GAAG;QAClC5H,IAAI,EAAE0G,aAAa,CAACmB,UAAU;QAC9BC,MAAM,KAAArC,MAAA,CAAAhG,kBAAA,CAAMiH,aAAa,CAACoB,MAAM,GAAArI,kBAAA,CAAKwD,oBAAoB,CAAC,CAAC,EAAC;QAC5D8E,KAAK,EAAAtI,kBAAA,CAAMiH,aAAa,CAACqB,KAAK,CAAC;QAC/BC,UAAU,EAAE,EAAE;QACdC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE1E,GAAG,CAACmE,cAAc,CAAC;QAC/BQ,QAAQ,KAAA1C,MAAA,CAAK2B,YAAY,OAAA3B,MAAA,CAAIvF,IAAI,CAAC8G,QAAQ,CAACoB,IAAI,OAAA3C,MAAA,CAAIvF,IAAI,CAAC8G,QAAQ,CAACqB,MAAM,CAAE;QACzEd,SAAS,EAAEL,iBAAiB,CAACzB,MAAM,CAAA/G,KAAA,CAAxBwI,iBAAiB,EAAAzH,kBAAA,CAAWgI,WAAW;MACpD,CAAC;MAEDG,MAAM,CAACE,MAAM,CAAEtH,IAAI,CAAC4C,gBAAgB,CAAC,CAAC,CAAC;MACvCwE,MAAM,CAACE,MAAM,CAAEtH,IAAI,CAAC0C,iBAAiB,CAAC,YAAY,CAAC,CAAC;MACpD0E,MAAM,CAACE,MAAM,CAAEtH,IAAI,CAAC6C,eAAe,CAACwD,YAAY,CAAC,CAAC;MAClDe,MAAM,CAACE,MAAM,CAAEtH,IAAI,CAAC;QAAER,IAAI,EAAE,WAAW;QAAE5C,KAAK,EAAEgJ,KAAK,CAACmB,SAAS,CAAC,CAAC,CAACF,IAAI,CAAC,KAAK;MAAE,CAAC,CAAC;;MAEhF;MACA,IAAI,MAAM,IAAInH,IAAI,EAAE;QAAA,IAAAoI,IAAA;QAClB,IAAMC,IAAa,GAAGrI,IAAI,CAACqI,IAAI,CAAC5C,GAAG,CAAC,UAAC6C,GAAG;UAAA,OAAM;YAC5CxI,IAAI,EAAEiC,SAAS,CAACwG,GAAG;YACnBrL,KAAK,EAAEoL,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGF,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH;UAClD,CAAC;QAAA,CAAC,CAAC;QACH,CAAAF,IAAA,GAAAV,MAAM,CAACE,MAAM,EAAEtH,IAAI,CAAA9B,KAAA,CAAA4J,IAAA,EAAA7I,kBAAA,CAAI8I,IAAI,EAAC;MAC9B;MAEA,IAAI,aAAa,IAAIrI,IAAI,EAAE;QAAA,IAAA0I,SAAA,GAAA/J,0BAAA,CACAqB,IAAI,CAAC2I,WAAW;UAAAC,KAAA;QAAA;UAAzC,KAAAF,SAAA,CAAAzJ,CAAA,MAAA2J,KAAA,GAAAF,SAAA,CAAAjN,CAAA,IAAAwB,IAAA,GAA2C;YAAA,IAAhC4L,UAAU,GAAAD,KAAA,CAAA1L,KAAA;YACnB,IAAI2L,UAAU,CAACC,IAAI,KAAK,MAAM,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO,EAAE;cAC7D;YACF;YAEA,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO,EAAE;cAAA,IAAAC,mBAAA;cAC/BrB,MAAM,CAACG,KAAK,CAAEvH,IAAI,CAChBwC,UAAU,EAAAiG,mBAAA,GAAC,IAAI,CAACtE,OAAO,CAACoD,KAAK,cAAAkB,mBAAA,cAAAA,mBAAA,GAAI,CAAC,CAAC,EAAE;gBACnCD,IAAI,EAAE9G,QAAQ,CAACgH,KAAK;gBACpBC,GAAG,EAAEJ,UAAU,CAACK;cAClB,CAAC,CACH,CAAC;cACD;YACF;YAEA,IAAIL,UAAU,CAACC,IAAI,KAAK,KAAK,IAAID,UAAU,CAACC,IAAI,KAAK,UAAU,EAAE;cAAA,IAAAK,oBAAA;cAC/DzB,MAAM,CAACG,KAAK,CAAEvH,IAAI,CAChBwC,UAAU,EAAAqG,oBAAA,GAAC,IAAI,CAAC1E,OAAO,CAACoD,KAAK,cAAAsB,oBAAA,cAAAA,oBAAA,GAAI,CAAC,CAAC,EAAE;gBACnCL,IAAI,EAAE9G,QAAQ,CAACoH,GAAG;gBAClBH,GAAG,EAAEJ,UAAU,CAACK;cAClB,CAAC,CACH,CAAC;cACD;YACF;YAEA,IAAIL,UAAU,CAACC,IAAI,KAAK,aAAa,EAAE;cACrCpB,MAAM,CAACwB,WAAW,GAAGL,UAAU,CAACK,WAAW;cAC3C;YACF;YAEA,IAAMG,eAAe,GAAGhH,gBAAgB,CAACwG,UAAU,CAACC,IAAI,EAAED,UAAU,CAACK,WAAW,CAAC;YAEjF,IAAIG,eAAe,EAAE;cACnB3B,MAAM,CAACE,MAAM,CAAEtH,IAAI,CAAC+I,eAAe,CAAC;cACpC;YACF;YAEA3B,MAAM,CAACK,KAAK,CAAEzH,IAAI,CAAC;cACjBR,IAAI,KAAAyF,MAAA,CAAKsD,UAAU,CAACC,IAAI,QAAAvD,MAAA,CAAKsD,UAAU,CAACK,WAAW,CAAG;cACtDI,MAAM,EAAEpH,MAAM,CAACqH,MAAM;cACrBC,KAAK,EAAEvH,KAAK,CAACwH,QAAQ;cACrB3B,UAAU,EAAE,EAAE;cACdC,KAAK,EAAE,EAAE;cACT2B,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,CAAC;YAClB,CAAC,CAAC;UACJ;QAAC,SAAAC,GAAA;UAAAlB,SAAA,CAAArN,CAAA,CAAAuO,GAAA;QAAA;UAAAlB,SAAA,CAAArM,CAAA;QAAA;MACH;MAEA,IAAIqK,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE5G,IAAI,EAAE;QACjB4H,MAAM,CAACI,UAAU,CAAExH,IAAI,CAAC;UAAER,IAAI,EAAE,SAAS;UAAE5C,KAAK,EAAEwJ,OAAO,CAAC5G;QAAK,CAAC,CAAC;MACnE;MAEA,IAAI,CAAA4G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmD,UAAU,IAAG,CAAC,EAAE;QAC3BnC,MAAM,CAACI,UAAU,CAAExH,IAAI,CAAC;UAAER,IAAI,EAAE,YAAY;UAAE5C,KAAK,KAAAqI,MAAA,CAAKvF,IAAI,CAAC8J,eAAe,GAAG,CAAC;QAAG,CAAC,CAAC;MACvF;MAEA,IAAMC,QAAQ,GAAG,IAAI,CAAC5D,aAAa,CAAE6D,SAAS,CAACtC,MAAM,CAAC;MAEtD,IAAI,CAACuC,kBAAkB,CAACC,GAAG,CAAClK,IAAI,CAACmK,EAAE,EAAEJ,QAAQ,CAAC;MAC9C,IAAI,CAACK,2BAA2B,CAAC9J,IAAI,CAACkG,aAAa,CAACmB,UAAU,CAAC;IACjE;EAAC;IAAA5G,GAAA;IAAA7D,KAAA,EAoBD,SAAAmN,WAAWA,CAACrK,IAAc,EAAEsK,OAA6B,EAAEC,IAAc,EAAQ;MAC/E,IAAMC,gBAAgB,GAAGD,IAAI,CAAC9D,KAAK,KAAK7C,4BAA4B;MACpE,IAAM6G,eAAe,GAAGF,IAAI,CAAC9D,KAAK,KAAK9C,2BAA2B;MAClE,IAAM+G,UAAU,GAAGF,gBAAgB,IAAIC,eAAe;MACtD,IAAME,sBAAsB,GAAG5G,gBAAgB,CAACwG,IAAI,CAAC;MACrD,IAAMK,qBAAqB,GAAG9G,eAAe,CAACyG,IAAI,CAAC;MACnD,IAAMM,UAAU,GAAGF,sBAAsB,IAAIC,qBAAqB;MAClE,IAAMb,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACa,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAE;MAEtD,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACY,QAAQ,CAACR,IAAI,CAACS,QAAQ,CAAC,IAAI,CAACH,UAAU,EAAE;QAAA,IAAAI,mBAAA,EAAAC,qBAAA;QACpE,IAAMC,WAAW,IAAAF,mBAAA,GAAG,IAAI,CAAC9E,aAAa,cAAA8E,mBAAA,uBAAlBA,mBAAA,CAAoBE,WAAW,CAACpB,QAAQ,CAAC;QAC7D,IAAI,CAACqB,eAAe,CAAClB,GAAG,CAACH,QAAQ,KAAAxE,MAAA,CAAAhG,kBAAA,EAAA2L,qBAAA,GAAO,IAAI,CAACE,eAAe,CAACN,GAAG,CAACf,QAAQ,CAAC,cAAAmB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,IAAGC,WAAW,EAAC,CAAC;QAChG;MACF;MAEA,IAAIzJ,iBAAA,CAAAyC,qBAAA,MAAI,EAACkH,iBAAgB,CAAC,CAAArO,IAAA,CAAtB,IAAI,EAAmBuN,IAAI,GAAG;QAChC;MACF;MAEA,IAAMe,QAAoB,GAAA/K,aAAA,CAAAA,aAAA,KACrBqC,gBAAgB,CAAC,CAAC;QACrB9C,IAAI,EAAEyK,IAAI,CAAC9D,KAAK;QAChB8E,KAAK,EAAEhB,IAAI,CAACiB,SAAS,CAACC,OAAO,CAAC,CAAC;QAC/BjC,KAAK,EAAEvH,KAAK,CAACyJ,OAAO;QACpBC,IAAI,EAAEnI,UAAU,CAAC;MAAC,EACnB;MAED,IAAIqH,UAAU,EAAE;QACd,IAAMe,KAAK,GAAGjB,sBAAsB,GAChC,IAAI,CAACkB,qBAAqB,CAACf,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,GACvC,IAAI,CAAC2B,oBAAoB,CAAChB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAE;QAE3C,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACY,QAAQ,CAACR,IAAI,CAACS,QAAQ,CAAC,EAAE;UACrDY,KAAK,CAACG,SAAS,CAACT,QAAQ,CAAC;UAEzB,IAAMU,WAAW,GAAGrB,sBAAsB,GAAG,IAAI,CAACsB,2BAA2B,GAAG,IAAI,CAACC,0BAA0B;UAE/GN,KAAK,CAACO,UAAU,CAAC,UAACC,UAAU,EAAK;YAAA,IAAAC,gBAAA;YAC/BD,UAAU,CAACtM,IAAI,GAAGmE,kBAAkB,CAACmI,UAAU,CAACtM,IAAK,CAAC;YACtDsM,UAAU,CAAC5C,KAAK,GAAGvH,KAAK,CAACwH,QAAQ;YACjCuC,WAAW,CAAC9B,GAAG,CAAClK,IAAI,CAACmK,EAAE,KAAA5E,MAAA,CAAAhG,kBAAA,EAAA8M,gBAAA,GAAOL,WAAW,CAAClB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,cAAAkC,gBAAA,cAAAA,gBAAA,GAAI,EAAE,IAAA9L,aAAA,CAAAA,aAAA,KAAQgK,IAAI;cAAEoB,IAAI,EAAES,UAAU,CAACT;YAAc,IAAG,CAAC;UAC/G,CAAC,CAAC;UACFC,KAAK,CAACU,QAAQ,CAAC,CAAC;UAChB;QACF;QACAV,KAAK,CAACG,SAAS,CAACT,QAAQ,CAAC;QAEzB;MACF;MAEA,IAAIZ,UAAU,EAAE;QACd,IAAMkB,MAAK,GAAG,IAAIlJ,iBAAiB,CAAC,CAAC;QACrCkJ,MAAK,CAACG,SAAS,CAACT,QAAQ,CAAC;QACzB,IAAId,gBAAgB,EAAE;UACpB,IAAI,CAACqB,qBAAqB,CAAC3B,GAAG,CAAClK,IAAI,CAACmK,EAAE,EAAEyB,MAAK,CAAC;QAChD,CAAC,MAAM;UACL,IAAI,CAACE,oBAAoB,CAAC5B,GAAG,CAAClK,IAAI,CAACmK,EAAE,EAAEyB,MAAK,CAAC;QAC/C;QACA;MACF;MAEA,IAAI,CAACzF,aAAa,CAAE4F,SAAS,CAAChC,QAAQ,EAAEwC,SAAS,EAAEjB,QAAQ,CAAC;IAC9D;EAAC;IAAAvK,GAAA;IAAA7D,KAAA,EAED,SAAAsP,SAASA,CAACxM,IAAc,EAAEsK,OAA6B,EAAEC,IAAc,EAAQ;MAC7E,IAAI7I,iBAAA,CAAAyC,qBAAA,MAAI,EAACkH,iBAAgB,CAAC,CAAArO,IAAA,CAAtB,IAAI,EAAmBuN,IAAI,GAAG;QAChC;MACF;MACA;MACA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACQ,QAAQ,CAACR,IAAI,CAACS,QAAQ,CAAC,EAAE;QACrD;MACF;MACA,IAAMjB,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACa,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAE;MACtD,IAAMK,gBAAgB,GAAGD,IAAI,CAAC9D,KAAK,KAAK7C,4BAA4B;MACpE,IAAM6G,eAAe,GAAGF,IAAI,CAAC9D,KAAK,KAAK9C,2BAA2B;MAClE,IAAMgH,sBAAsB,GAAG5G,gBAAgB,CAACwG,IAAI,CAAC;MACrD,IAAMK,qBAAqB,GAAG9G,eAAe,CAACyG,IAAI,CAAC;MACnD,IAAMkC,WAAW,GAAGhC,eAAe,IAAIG,qBAAqB;MAC5D,IAAM8B,MAAM,GAAGlC,gBAAgB,IAAIC,eAAe,IAAIE,sBAAsB,IAAIC,qBAAqB;MAErG,IAAI8B,MAAM,EAAE;QACV,IAAMd,KAAK,GAAGa,WAAW,GAAG,IAAI,CAACX,oBAAoB,CAAChB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,GAAI,IAAI,CAAC0B,qBAAqB,CAACf,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAE;QAE9GyB,KAAK,CAACO,UAAU,CAAC,UAACC,UAAU,EAAK;UAAA,IAAAO,qBAAA;UAC/B,IAAAC,KAAA,IAAAD,qBAAA,GAAmCtJ,sBAAsB,CAAC+I,UAAU,CAACrE,KAAK,CAAC,cAAA4E,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;YAAAE,YAAA,GAAAD,KAAA,CAAzEtD,MAAM;YAANA,MAAM,GAAAuD,YAAA,cAAG3K,MAAM,CAACqH,MAAM,GAAAsD,YAAA;UAC9BT,UAAU,CAAC9C,MAAM,GAAGiB,IAAI,CAACuC,KAAK,GAAG5K,MAAM,CAAC6K,MAAM,GAAGzD,MAAM;UACvD8C,UAAU,CAAC5C,KAAK,GAAGvH,KAAK,CAACwH,QAAQ;UACjC,IAAIc,IAAI,CAACuC,KAAK,EAAE;YACdV,UAAU,CAACzC,aAAa,GAAApJ,aAAA,KAAQ6B,2BAA2B,CAACmI,IAAI,CAACuC,KAAK,CAAC,CAAE;UAC3E;QACF,CAAC,CAAC;QACFlB,KAAK,CAACU,QAAQ,CAAC;UACbU,QAAQ,EAAEzC,IAAI,CAACyC;QACjB,CAAC,CAAC;QACF;MACF;MAEA,IAAM7B,WAAW,GAAG,IAAI,CAAChF,aAAa,CAAEgF,WAAW,CAACpB,QAAQ,CAAC;MAE7D,IAAI,CAACoB,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAAChF,aAAa,CAAEgG,UAAU,CAAChB,WAAW,EAAE,UAACiB,UAAU,EAAK;QAAA,IAAAa,sBAAA;QAC1D,IAAAC,KAAA,IAAAD,sBAAA,GAAmC5J,sBAAsB,CAAC+I,UAAU,CAACrE,KAAK,CAAC,cAAAkF,sBAAA,cAAAA,sBAAA,GAAI,CAAC,CAAC;UAAAE,YAAA,GAAAD,KAAA,CAAzE5D,MAAM;UAANA,MAAM,GAAA6D,YAAA,cAAGjL,MAAM,CAACqH,MAAM,GAAA4D,YAAA;QAC9Bf,UAAU,CAAC9C,MAAM,GAAGiB,IAAI,CAACuC,KAAK,GAAG5K,MAAM,CAAC6K,MAAM,GAAGzD,MAAM;QACvD8C,UAAU,CAAC5C,KAAK,GAAGvH,KAAK,CAACwH,QAAQ;QACjC,IAAIc,IAAI,CAACuC,KAAK,EAAE;UACdV,UAAU,CAACzC,aAAa,GAAApJ,aAAA,KAAQ6B,2BAA2B,CAACmI,IAAI,CAACuC,KAAK,CAAC,CAAE;QAC3E;MACF,CAAC,CAAC;MACF,IAAI,CAAC3G,aAAa,CAAEmG,QAAQ,CAACnB,WAAW,EAAE;QAAE6B,QAAQ,EAAEzC,IAAI,CAACyC;MAAS,CAAC,CAAC;IACxE;EAAC;IAAAjM,GAAA;IAAA7D,KAAA;MAAA,IAAAkQ,UAAA,GAAA9O,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAED,SAAA0P,QAAgBrN,IAAc,EAAE0H,MAA4B;QAAA,IAAA4F,KAAA;UAAAC,qBAAA;UAAAC,qBAAA;UAAAC,sBAAA;QAAA,IAAA1D,QAAA,EAAA2D,QAAA,EAAAC,MAAA,EAAAb,KAAA,EAAAc,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAAxG,WAAA,EAAAyG,gBAAA,EAAAC,eAAA,EAAAC,wBAAA,EAAAC,uBAAA,EAAA/C,eAAA,EAAAgD,mBAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAjT,CAAA,EAAAkT,UAAA,EAAAC,QAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAlD,UAAA,EAAAmD,QAAA;QAAA,OAAA9R,YAAA,GAAAC,CAAA,WAAA8R,QAAA;UAAA,kBAAAA,QAAA,CAAA/T,CAAA;YAAA;cACpDsO,QAAQ,GAAG,IAAI,CAACE,kBAAkB,CAACa,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,EACrD;cACMuD,QAAQ,GAAGhG,MAAM,CAAC+H,aAAa,KAAKlD,SAAS,GAAG7E,MAAM,CAAC+H,aAAa,GAAG/H,MAAM,CAACgI,WAAW;cACzF/B,MAAM,UAAApI,MAAA,CAAU1D,OAAO,CAAC8N,GAAG,cAAApK,MAAA,CAAWmI,QAAQ;cAC9CZ,KAAK,GAAGpF,MAAM,CAACoF,KAAK,EAC1B;cAAAc,qBAAA,GAC8D5N,IAAI,CAACuG,MAAM,CAACc,SAAS,CAAC,CAAC,EAAAwG,sBAAA,GAAA1O,QAAA,CAAAyO,qBAAA,GAA5EE,iBAAiB,GAAAD,sBAAA,KAAEE,cAAc,GAAAF,sBAAA,KAAKtG,WAAW,GAAAsG,sBAAA,CAAAjO,KAAA;cACpDoO,gBAAgB,GAAG,IAAI,CAACnC,qBAAqB,CAACf,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC;cAC1D8D,eAAe,GAAG,IAAI,CAACnC,oBAAoB,CAAChB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC;cAE9D,IAAI,CAAChE,aAAa,CAAEyJ,UAAU,CAAC7F,QAAQ,EAAE,UAAC8F,UAAU,EAAK;gBACvDA,UAAU,CAACjI,MAAM,CAACtH,IAAI,CAAC2C,YAAY,CAAC,CAAC,CAAC;gBACtC4M,UAAU,CAACjI,MAAM,CAACtH,IAAI,CAAC8C,cAAc,CAACuK,MAAM,CAAC,CAAC;gBAE9C,IAAIG,iBAAiB,IAAI,CAACxL,QAAQ,CAACuN,UAAU,EAAE9N,SAAS,CAAC+N,YAAY,CAAC,EAAE;kBACtED,UAAU,CAACjI,MAAM,CAACtH,IAAI,CAAC;oBAAER,IAAI,EAAEiC,SAAS,CAAC+N,YAAY;oBAAE5S,KAAK,EAAE4Q;kBAAkB,CAAC,CAAC;gBACpF;gBAEA,IAAIR,KAAI,CAAC7I,OAAO,CAACC,UAAU,IAAIqJ,cAAc,IAAI,CAACzL,QAAQ,CAACuN,UAAU,EAAE9N,SAAS,CAACgO,KAAK,CAAC,EAAE;kBACvFF,UAAU,CAACjI,MAAM,CAACtH,IAAI,CAAC;oBAAER,IAAI,EAAEiC,SAAS,CAACgO,KAAK;oBAAE7S,KAAK,EAAE6Q;kBAAe,CAAC,CAAC;gBAC1E;gBAEA,IAAIxG,WAAW,CAAC1K,MAAM,GAAG,CAAC,IAAI,CAACyF,QAAQ,CAACuN,UAAU,EAAE9N,SAAS,CAACiO,SAAS,CAAC,EAAE;kBACxEH,UAAU,CAACjI,MAAM,CAACtH,IAAI,CAAC;oBAAER,IAAI,EAAEiC,SAAS,CAACiO,SAAS;oBAAE9S,KAAK,EAAEqK,WAAW,CAACJ,IAAI,CAAC,KAAK;kBAAE,CAAC,CAAC;gBACvF;gBAEA,IAAI2F,KAAK,EAAE;kBACT+C,UAAU,CAAClG,aAAa,GAAApJ,aAAA,KAAQ6B,2BAA2B,CAAC0K,KAAK,CAAC,CAAE;gBACtE,CAAC,MAAM;kBAAA,IAAAmD,iBAAA;kBACL,IAAMC,UAAU,IAAAD,iBAAA,GAAGjQ,IAAI,CAAC2I,WAAW,cAAAsH,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAkBE,IAAI,CACvC,UAACtH,UAAU;oBAAA,OAAKA,UAAU,CAACC,IAAI,KAAK,MAAM,IAAID,UAAU,CAACC,IAAI,KAAK,OAAO;kBAAA,CAC3E,CAAC,cAAAmH,iBAAA,uBAFkBA,iBAAA,CAEhB/G,WAAW;kBAEd,IAAIgH,UAAU,EAAE;oBACdL,UAAU,CAAClG,aAAa,GAAApJ,aAAA,CAAAA,aAAA,KAAQsP,UAAU,CAAClG,aAAa;sBAAEyG,OAAO,EAAEF;oBAAU,EAAE;kBACjF;gBACF;gBAEAL,UAAU,CAACvG,MAAM,GAAGpF,mBAAmB,CAACwD,MAAM,CAAC4B,MAAM,EAAEtJ,IAAI,CAACqQ,cAAc,CAAC;gBAC3ER,UAAU,CAACrG,KAAK,GAAGvH,KAAK,CAACwH,QAAQ;cACnC,CAAC,CAAC;cAEIyE,wBAAwB,IAAAX,qBAAA,GAAG,IAAI,CAACtB,2BAA2B,CAACnB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,cAAAoD,qBAAA,cAAAA,qBAAA,GAAI,EAAE;cAC9EY,uBAAuB,IAAAX,qBAAA,GAAG,IAAI,CAACtB,0BAA0B,CAACpB,GAAG,CAAC9K,IAAI,CAACmK,EAAE,CAAC,cAAAqD,qBAAA,cAAAA,qBAAA,GAAI,EAAE;cAC5EpC,eAAe,IAAAqC,sBAAA,GAAG,IAAI,CAACrC,eAAe,CAACN,GAAG,CAACf,QAAQ,CAAC,cAAA0D,sBAAA,cAAAA,sBAAA,GAAI,EAAE;cAE1DW,mBAAmB,GAAG,IAAI5J,GAAG,CAAyE,CAAC;cAEzG6J,eAAe,GAAG,CAAC;cAAAC,UAAA,GAAA3P,0BAAA,CAEAuP,wBAAwB;cAAA;gBAA/C,KAAAI,UAAA,CAAArP,CAAA,MAAAsP,MAAA,GAAAD,UAAA,CAAA7S,CAAA,IAAAwB,IAAA,GAAiD;kBAAtCmS,SAAQ,GAAAb,MAAA,CAAArR,KAAA;kBACjBkR,mBAAmB,CAAClE,GAAG,CAACmE,eAAe,EAAE;oBACvCM,QAAQ,EAAES,SAAQ,CAACzD,IAAI;oBACvBe,MAAM,EAAE,IAAI;oBACZ0C,QAAQ,EAARA;kBACF,CAAC,CAAC;kBACFf,eAAe,EAAE;gBACnB;cAAC,SAAAzE,GAAA;gBAAA0E,UAAA,CAAAjT,CAAA,CAAAuO,GAAA;cAAA;gBAAA0E,UAAA,CAAAjS,CAAA;cAAA;cAAAoS,UAAA,GAAA9P,0BAAA,CAEsByM,eAAe;cAAA;gBAAtC,KAAAqD,UAAA,CAAAxP,CAAA,MAAAyP,MAAA,GAAAD,UAAA,CAAAhT,CAAA,IAAAwB,IAAA,GAAwC;kBAA7B0R,QAAQ,GAAAD,MAAA,CAAAxR,KAAA;kBACjBkR,mBAAmB,CAAClE,GAAG,CAACmE,eAAe,EAAE;oBACvCM,QAAQ,EAARA,QAAQ;oBACRjC,MAAM,EAAE;kBACV,CAAC,CAAC;kBACF2B,eAAe,EAAE;gBACnB;cAAC,SAAAzE,GAAA;gBAAA6E,UAAA,CAAApT,CAAA,CAAAuO,GAAA;cAAA;gBAAA6E,UAAA,CAAApS,CAAA;cAAA;cAAAuS,UAAA,GAAAjQ,0BAAA,CAEsBwP,uBAAuB;cAAA;gBAA9C,KAAAS,UAAA,CAAA3P,CAAA,MAAA4P,MAAA,GAAAD,UAAA,CAAAnT,CAAA,IAAAwB,IAAA,GAAgD;kBAArCmS,UAAQ,GAAAP,MAAA,CAAA3R,KAAA;kBACjBkR,mBAAmB,CAAClE,GAAG,CAACmE,eAAe,EAAE;oBACvCM,QAAQ,EAAES,UAAQ,CAACzD,IAAI;oBACvBe,MAAM,EAAE,IAAI;oBACZ0C,QAAQ,EAARA;kBACF,CAAC,CAAC;kBACFf,eAAe,EAAE;gBACnB;cAAC,SAAAzE,GAAA;gBAAAgF,UAAA,CAAAvT,CAAA,CAAAuO,GAAA;cAAA;gBAAAgF,UAAA,CAAAvS,CAAA;cAAA;cAEQR,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAG6L,MAAM,CAACgC,WAAW,CAAC7M,MAAM;gBAAA2S,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cACrCsT,UAAU,GAAGrH,MAAM,CAACgC,WAAW,CAAC7N,CAAC,CAAC;cAClCmT,QAAQ,GAAGZ,mBAAmB,CAACtD,GAAG,CAACjP,CAAC,CAAC;cAAA,MAEvCmT,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEtC,MAAM;gBAAA8C,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cAAA,OAAA+T,QAAA,CAAA9S,CAAA;YAAA;cAAA,MAETsS,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEL,QAAQ;gBAAAa,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cAAA+T,QAAA,CAAA/T,CAAA;cAAA,OACrB,IAAI,CAAC6U,iBAAiB,CAACvG,QAAQ,EAAEiF,QAAQ,CAACL,QAAQ,EAAEI,UAAU,CAAC;YAAA;cAAAS,QAAA,CAAA/T,CAAA;cAAA;YAAA;cAAA+T,QAAA,CAAA/T,CAAA;cAAA,OAE/D,IAAI,CAAC6U,iBAAiB,CAACvG,QAAQ,EAAEwC,SAAS,EAAEwC,UAAU,CAAC;YAAA;cATlBlT,CAAC,EAAE;cAAA2T,QAAA,CAAA/T,CAAA;cAAA;YAAA;cAalD,IAAIiM,MAAM,CAAC6I,MAAM,CAAC1T,MAAM,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAACsJ,aAAa,CAAEqK,eAAe,CACjCzG,QAAQ,EACRwC,SAAS,EACT,QAAQ,EACRkE,MAAM,CAAC1Q,IAAI,CAACwC,SAAS,CAACmF,MAAM,CAAC6I,MAAM,CAACpJ,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EACvD;kBACEuJ,WAAW,EAAE5O,WAAW,CAAC6O;gBAC3B,CACF,CAAC;cACH;cAEA,IAAIjJ,MAAM,CAACkJ,MAAM,CAAC/T,MAAM,GAAG,CAAC,EAAE;gBAC5B,IAAI,CAACsJ,aAAa,CAAEqK,eAAe,CACjCzG,QAAQ,EACRwC,SAAS,EACT,QAAQ,EACRkE,MAAM,CAAC1Q,IAAI,CAACwC,SAAS,CAACmF,MAAM,CAACkJ,MAAM,CAACzJ,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EACvD;kBACEuJ,WAAW,EAAE5O,WAAW,CAAC6O;gBAC3B,CACF,CAAC;cACH;;cAEA;cACA;cAES9U,EAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,EAAC,GAAG6L,MAAM,CAACgC,WAAW,CAAC7M,MAAM;gBAAA2S,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cACrCsT,WAAU,GAAGrH,MAAM,CAACgC,WAAW,CAAC7N,EAAC,CAAC;cAClCmT,SAAQ,GAAGZ,mBAAmB,CAACtD,GAAG,CAACjP,EAAC,CAAC;cAAA,MAEvCmT,SAAQ,aAARA,SAAQ,eAARA,SAAQ,CAAEtC,MAAM,IAAIsC,SAAQ,CAACI,QAAQ;gBAAAI,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cACjC2T,QAAQ,GAAGJ,SAAQ,CAACI,QAAQ;cAC5BC,YAAY,GAAGnB,wBAAwB,CAACnD,QAAQ,CAACqE,QAAQ,CAAC;cAC1DE,WAAW,GAAGD,YAAY,GAAGrB,gBAAgB,GAAGC,eAAe;cAAA,MAEjEc,WAAU,CAAC2B,WAAW,KAAKlO,mCAAmC;gBAAAgN,QAAA,CAAA/T,CAAA;gBAAA;cAAA;cAAA+T,QAAA,CAAA/T,CAAA;cAAA,OAC1D,IAAI,CAAC6U,iBAAiB,CAACvG,QAAQ,EAAEqF,QAAQ,CAACzD,IAAI,EAAEoD,WAAU,CAAC;YAAA;cAAA,OAAAS,QAAA,CAAA9S,CAAA;YAAA;cAInE,IAAI4S,WAAW,EAAE;gBACTlD,UAAU,GAAGkD,WAAW,CAACuB,cAAc,CAACzB,QAAQ,CAACzD,IAAI,CAAC;gBAC5D,IAAIS,UAAU,EAAE;kBACRmD,QAAQ,GAAGD,WAAW,CAACwB,aAAa,CAAC/B,WAAU,EAAE,IAAI,CAAC5I,aAAa,CAAEC,MAAM,CAAC;kBAClFgG,UAAU,CAAC1C,WAAW,CAACpJ,IAAI,CAAC;oBAC1BR,IAAI,EAAEiP,WAAU,CAACjP,IAAI;oBACrBgJ,IAAI,EAAEiG,WAAU,CAAC2B,WAAW;oBAC5BK,MAAM,EAAExB;kBACV,CAAC,CAAC;gBACJ;cACF;YAAC;cAxB0C1T,EAAC,EAAE;cAAA2T,QAAA,CAAA/T,CAAA;cAAA;YAAA;cA4BlD,IAAI,CAAC0K,aAAa,CAAEyJ,UAAU,CAAC7F,QAAQ,EAAE,UAAC8F,UAAU,EAAK;gBACvD,IAAMmB,YAAY,GAAGnB,UAAU,CAACjI,MAAM,CAACqJ,MAAM,CAA0B,UAACC,GAAG,EAAEC,KAAK,EAAK;kBACrF,IAAI,CAACD,GAAG,CAACC,KAAK,CAACrR,IAAI,CAAC,EAAE;oBACpBoR,GAAG,CAACC,KAAK,CAACrR,IAAI,CAAC,GAAG,EAAE;kBACtB;kBAEAoR,GAAG,CAACC,KAAK,CAACrR,IAAI,CAAC,CAACQ,IAAI,CAAC6Q,KAAK,CAAC;kBAE3B,OAAOD,GAAG;gBACZ,CAAC,EAAE,CAAC,CAAC,CAAC;gBACN,IAAME,SAAS,GAAGlV,MAAM,CAACgE,IAAI,CAAC8Q,YAAY,CAAC,CAACK,OAAO,CAAC,UAACC,SAAS,EAAK;kBACjE,IAAMC,WAAW,GAAGP,YAAY,CAACM,SAAS,CAAC;kBAE3C,IACEA,SAAS,KAAKvP,SAAS,CAACgO,KAAK,IAC7BuB,SAAS,KAAKvP,SAAS,CAAC+N,YAAY,IACpCwB,SAAS,KAAKvP,SAAS,CAACiO,SAAS,EACjC;oBACA,OAAOuB,WAAW,CAAC3R,KAAK,CAAC,CAAC,CAAC,CAAC;kBAC9B;kBAEA,OAAO2R,WAAW;gBACpB,CAAC,CAAC;gBAEF,IAAIvD,gBAAgB,EAAE;kBAAA,IAAAwD,iBAAA;kBACpB,CAAAA,iBAAA,GAAA3B,UAAU,CAAC9H,KAAK,EAAC0J,OAAO,CAAAjT,KAAA,CAAAgT,iBAAA,EAAAjS,kBAAA,CAAIyO,gBAAgB,CAACjG,KAAK,EAAC;kBACnDuF,KAAI,CAACzB,qBAAqB,UAAO,CAAC7L,IAAI,CAACmK,EAAE,CAAC;gBAC5C;gBAEA,IAAI8D,eAAe,EAAE;kBAAA,IAAAyD,kBAAA;kBACnB,CAAAA,kBAAA,GAAA7B,UAAU,CAAC9H,KAAK,EAACzH,IAAI,CAAA9B,KAAA,CAAAkT,kBAAA,EAAAnS,kBAAA,CAAI0O,eAAe,CAAClG,KAAK,EAAC;kBAC/CuF,KAAI,CAACxB,oBAAoB,UAAO,CAAC9L,IAAI,CAACmK,EAAE,CAAC;gBAC3C;gBAEA0F,UAAU,CAACjI,MAAM,GAAGwJ,SAAS;cAC/B,CAAC,CAAC;cACF,IAAI,CAACjL,aAAa,CAAEwL,QAAQ,CAAC5H,QAAQ,EAAE;gBAAEiD,QAAQ,EAAEtF,MAAM,CAACsF;cAAS,CAAC,CAAC;cACrE,IAAI,CAAC7G,aAAa,CAAEyL,SAAS,CAAC7H,QAAQ,CAAC;YAAC;cAAA,OAAAyF,QAAA,CAAA9S,CAAA;UAAA;QAAA,GAAA2Q,OAAA;MAAA,CACzC;MAAA,SAvLKwE,SAASA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAA3E,UAAA,CAAA5O,KAAA,OAAAD,SAAA;MAAA;MAAA,OAATsT,SAAS;IAAA;EAAA;IAAA9Q,GAAA;IAAA7D,KAAA;MAAA,IAAA8U,kBAAA,GAAA1T,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAyLf,SAAAsU,SAAA;QAAA,IAAAC,MAAA;QAAA,IAAAC,gBAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,OAAA9U,YAAA,GAAAC,CAAA,WAAA8U,SAAA;UAAA,kBAAAA,SAAA,CAAA/W,CAAA;YAAA;cACQ0W,gBAAgB,GAAG,IAAI,CAACjM,KAAK,CAACuM,QAAQ,CAAC,CAAC,CAACrS,MAAM,CAAC,UAAAsS,KAAA,EAAe;gBAAA,IAAZjM,KAAK,GAAAiM,KAAA,CAALjM,KAAK;gBAC5D,IAAMD,aAAa,GAAGrE,yBAAyB,CAACsE,KAAK,CAAC;gBAEtD,OAAO,CAACyL,MAAI,CAAC9H,2BAA2B,CAACW,QAAQ,CAACvE,aAAa,CAACmB,UAAU,CAAC;cAC7E,CAAC,CAAC;cAAAyK,UAAA,GAAAzT,0BAAA,CAEqBwT,gBAAgB;cAAAK,SAAA,CAAAlW,CAAA;cAAA8V,UAAA,CAAAnT,CAAA;YAAA;cAAA,KAAAoT,MAAA,GAAAD,UAAA,CAAA3W,CAAA,IAAAwB,IAAA;gBAAAuV,SAAA,CAAA/W,CAAA;gBAAA;cAAA;cAA5B6W,QAAQ,GAAAD,MAAA,CAAAnV,KAAA;cACjB,IAAI,CAACoJ,WAAW,CAACgM,QAAQ,CAAC;cAACE,SAAA,CAAA/W,CAAA;cAAA,OACrB,IAAI,CAACoW,SAAS,CAACS,QAAQ,EAAE;gBAC7BhJ,MAAM,EAAEpH,MAAM,CAACyQ,OAAO;gBACtBjJ,WAAW,EAAE,EAAE;gBACfsD,QAAQ,EAAE,CAAC;gBACX4F,MAAM,EAAE,EAAE;gBACVnD,aAAa,EAAE,CAAC;gBAChBC,WAAW,EAAE,CAAC;gBACdmD,KAAK,EAAE,CAAC;gBACR9K,KAAK,EAAE,EAAE;gBACT6I,MAAM,EAAE,EAAE;gBACVL,MAAM,EAAE,EAAE;gBACV/E,SAAS,EAAE,IAAI,CAACsH,eAAe;gBAC/BnK,WAAW,EAAE;cACf,CAAC,CAAC;YAAA;cAAA6J,SAAA,CAAA/W,CAAA;cAAA;YAAA;cAAA+W,SAAA,CAAA/W,CAAA;cAAA;YAAA;cAAA+W,SAAA,CAAAlW,CAAA;cAAAiW,EAAA,GAAAC,SAAA,CAAA/V,CAAA;cAAA2V,UAAA,CAAA/W,CAAA,CAAAkX,EAAA;YAAA;cAAAC,SAAA,CAAAlW,CAAA;cAAA8V,UAAA,CAAA/V,CAAA;cAAA,OAAAmW,SAAA,CAAAnW,CAAA;YAAA;cAAA,OAAAmW,SAAA,CAAA9V,CAAA;UAAA;QAAA,GAAAuV,QAAA;MAAA,CAEL;MAAA,SAxBKc,iBAAiBA,CAAA;QAAA,OAAAf,kBAAA,CAAAxT,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAjBwU,iBAAiB;IAAA;EAAA;IAAAhS,GAAA;IAAA7D,KAAA;MAAA,IAAA8V,MAAA,GAAA1U,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CA0BvB,SAAAsV,SAAA;QAAA,OAAAxV,YAAA,GAAAC,CAAA,WAAAwV,SAAA;UAAA,kBAAAA,SAAA,CAAAzX,CAAA;YAAA;cAAAyX,SAAA,CAAAzX,CAAA;cAAA,OACQ,IAAI,CAACsX,iBAAiB,CAAC,CAAC;YAAA;cAE9B,IAAI,CAAC5M,aAAa,CAAEgN,oBAAoB,CAAC,CAAC;cAC1C,IAAI,CAAChN,aAAa,CAAEiN,0BAA0B,CAAC,CAAC;YAAC;cAAA,OAAAF,SAAA,CAAAxW,CAAA;UAAA;QAAA,GAAAuW,QAAA;MAAA,CAClD;MAAA,SALKI,KAAKA,CAAA;QAAA,OAAAL,MAAA,CAAAxU,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAL8U,KAAK;IAAA;EAAA;IAAAtS,GAAA;IAAA7D,KAAA,EAOX,SAAAoW,aAAaA,CAAA,EAAY;MACvB,OAAO,KAAK;IACd;EAAC;IAAAvS,GAAA;IAAA7D,KAAA,EAED,SAAQqW,0BAA0BA,CAACC,kBAA0B,EAAEpD,OAAmC,EAAE;MAClG,IAAAqD,aAAA,GAAkCrD,OAAO,CAACsD,IAAI;QAAtC5T,IAAI,GAAA2T,aAAA,CAAJ3T,IAAI;QAAA6T,qBAAA,GAAAF,aAAA,CAAE3L,UAAU;QAAVA,UAAU,GAAA6L,qBAAA,cAAG,EAAE,GAAAA,qBAAA;MAE7B,IAAI,CAACxN,aAAa,CAAEgG,UAAU,CAACqH,kBAAkB,EAAE,UAACjJ,IAAI,EAAK;QAAA,IAAAqJ,gBAAA;QAC3D,IAAI9T,IAAI,EAAE;UACRyK,IAAI,CAACzK,IAAI,GAAGA,IAAI;QAClB;QAEA,CAAA8T,gBAAA,GAAArJ,IAAI,CAACzC,UAAU,EAACxH,IAAI,CAAA9B,KAAA,CAAAoV,gBAAA,EAAArU,kBAAA,CAAIuI,UAAU,EAAC;MACrC,CAAC,CAAC;IACJ;EAAC;IAAA/G,GAAA;IAAA7D,KAAA;MAAA,IAAA2W,kBAAA,GAAAvV,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAED,SAAAmW,SACE/J,QAAgB,EAChByJ,kBAAsC,EACtCzE,UAKC;QAAA,IAAAgF,oBAAA,EAAA3D,OAAA,EAAA4D,UAAA,EAAAtD,WAAA,EAAAuD,cAAA,EAAAC,YAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,QAAA;QAAA,OAAA5W,YAAA,GAAAC,CAAA,WAAA4W,SAAA;UAAA,kBAAAA,SAAA,CAAA7Y,CAAA;YAAA;cAAA,MAEG,CAACsT,UAAU,CAACwF,IAAI,IAAI,CAACxF,UAAU,CAACnN,IAAI;gBAAA0S,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAAA,OAAA6Y,SAAA,CAAA5X,CAAA;YAAA;cAIlCqX,oBAAoB,GAAGhF,UAAU,CAAC2B,WAAW,KAAKlO,mCAAmC;cAAA,MAEvFuR,oBAAoB,IAAI,CAAChF,UAAU,CAACwF,IAAI;gBAAAD,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAAA,OAAA6Y,SAAA,CAAA5X,CAAA;YAAA;cAAA,KAIxCqX,oBAAoB;gBAAAO,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAChB2U,OAAO,GAAGoE,IAAI,CAACC,KAAK,CAAC1F,UAAU,CAACwF,IAAI,CAAE5U,QAAQ,CAAC,CAAC,CAAC;cAAA,MAEnDyQ,OAAO,CAACtH,IAAI,KAAK,eAAe;gBAAAwL,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAClC,IAAI,CAAC8X,0BAA0B,CAACC,kBAAkB,EAAGpD,OAAO,CAAC;cAAC,OAAAkE,SAAA,CAAA5X,CAAA;YAAA;cAIhE,IAAI,CAACyJ,aAAa,CAAEuO,oBAAoB,CAAC3K,QAAQ,EAAE,CAACqG,OAAO,CAAC,CAAC;cAAC,OAAAkE,SAAA,CAAA5X,CAAA;YAAA;cAI1DsX,UAAU,GAAG,IAAI,CAAC7N,aAAa,CAAE4F,SAAS,CAAChC,QAAQ,EAAEyJ,kBAAkB,EAAE;gBAAE1T,IAAI,EAAEiP,UAAU,CAACjP;cAAK,CAAC,CAAC,EAEzG;cACA;cACA;cACA,IAAIkU,UAAU,EAAE;gBACd,IAAI,CAAC7N,aAAa,CAAEmG,QAAQ,CAAC0H,UAAU,EAAEzH,SAAS,CAAC;cACrD;cAAC,KAEGwC,UAAU,CAACwF,IAAI;gBAAAD,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cACjB,IAAI,CAAC0K,aAAa,CAAEqK,eAAe,CAACzG,QAAQ,EAAEiK,UAAU,EAAEjF,UAAU,CAACjP,IAAI,EAAEiP,UAAU,CAACwF,IAAI,EAAE;gBAC1F7D,WAAW,EAAE3B,UAAU,CAAC2B;cAC1B,CAAC,CAAC;cAAC4D,SAAA,CAAA7Y,CAAA;cAAA;YAAA;cAAA,IACOkG,UAAU,CAACoN,UAAU,CAACnN,IAAK,CAAC;gBAAA0S,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAAA,OAAA6Y,SAAA,CAAA5X,CAAA;YAAA;cAGhCgU,WAAW,GACf3B,UAAU,CAACjP,IAAI,KAAK,OAAO,IAAIiP,UAAU,CAAC2B,WAAW,KAAK,iBAAiB,GACvE,yCAAyC,GACzC3B,UAAU,CAAC2B,WAAW;cAE5B,IAAI,CAACvK,aAAa,CAAEqK,eAAe,CAACzG,QAAQ,EAAEiK,UAAU,EAAEjF,UAAU,CAACjP,IAAI,EAAEiP,UAAU,CAACnN,IAAI,EAAG;gBAC3F8O,WAAW,EAAXA;cACF,CAAC,CAAC;YAAC;cAAA,IAGA3B,UAAU,CAACjP,IAAI,CAAC6U,KAAK,CAAC9Q,aAAa,CAAC;gBAAAyQ,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAAA,OAAA6Y,SAAA,CAAA5X,CAAA;YAAA;cAInCuX,cAAc,GAAGlF,UAAU,CAACnN,IAAI,CAAE8D,OAAO,CAAC7B,aAAa,EAAE,EAAE,CAAC;cAAA,KAE9D,IAAI,CAAC+Q,cAAc,CAAC7J,QAAQ,CAACkJ,cAAc,CAAC;gBAAAK,SAAA,CAAA7Y,CAAA;gBAAA;cAAA;cAAA,OAAA6Y,SAAA,CAAA5X,CAAA;YAAA;cAAA4X,SAAA,CAAA7Y,CAAA;cAAA,OAIrBgI,iBAAiB,IAAA8B,MAAA,CAAI0O,cAAc,gBAAa,CAAC;YAAA;cAAtEC,YAAY,GAAAI,SAAA,CAAA7X,CAAA;cAAA6X,SAAA,CAAA7Y,CAAA;cAAA,OACWgI,iBAAiB,IAAA8B,MAAA,CAAI0O,cAAc,kBAAe,CAAC;YAAA;cAA1EE,cAAc,GAAAG,SAAA,CAAA7X,CAAA;cAAA6X,SAAA,CAAA7Y,CAAA;cAAA,OACKgI,iBAAiB,IAAA8B,MAAA,CAAI0O,cAAc,cAAW,CAAC;YAAA;cAAlEG,UAAU,GAAAE,SAAA,CAAA7X,CAAA;cACV4X,QAAQ,GAAGtF,UAAU,CAACjP,IAAI,CAAC4F,OAAO,CAAC7B,aAAa,EAAE,EAAE,CAAC;cAE3D,IAAI,CAACsC,aAAa,CAAEqK,eAAe,CACjCzG,QAAQ,EACRwC,SAAS,EACT8H,QAAQ,EACR5D,MAAM,CAAC1Q,IAAI,CACTyU,IAAI,CAACK,SAAS,CAAC;gBACbC,QAAQ,EAAEX,cAAc;gBACxBY,MAAM,EAAEb,YAAY;gBACpBc,IAAI,EAAEZ,UAAU;gBAChBtU,IAAI,EAAEuU;cACR,CAAwB,CAAC,EACzB,OACF,CAAC,EACD;gBACE3D,WAAW,EAAE5O,WAAW,CAACmT,SAAS;gBAClCC,aAAa,EAAE;cACjB,CACF,CAAC;cAED,IAAI,CAACN,cAAc,CAACtU,IAAI,CAAC2T,cAAc,CAAC;YAAC;cAAA,OAAAK,SAAA,CAAA5X,CAAA;UAAA;QAAA,GAAAoX,QAAA;MAAA,CAC1C;MAAA,SA7FaxD,iBAAiBA,CAAA6E,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAxB,kBAAA,CAAArV,KAAA,OAAAD,SAAA;MAAA;MAAA,OAAjB+R,iBAAiB;IAAA;EAAA;IAAAvP,GAAA;IAAA7D,KAAA,EA+F/B,SAAAoY,OAAOA,CAAA,EAAS;MACd,OAAO,IAAI;IACb;EAAC;AAAA;;AAGH;AACA;AACA;AAFA,SAAAjK,kBAldoBd,IAAc,EAAE;EAChC,IAAI,CAAC,IAAI,CAAC9F,OAAO,CAACE,MAAM,IAAI4F,IAAI,CAACS,QAAQ,KAAK,WAAW,EAAE;IACzD,OAAO,IAAI;EACb;;EAEA;EACA,IAAIT,IAAI,CAACS,QAAQ,KAAK,QAAQ,IAAIT,IAAI,CAAC9D,KAAK,KAAK,kBAAkB,EAAE;IACnE,OAAO,IAAI;EACb;;EAEA;EACA,IAAI8D,IAAI,CAAC9D,KAAK,KAAK,gBAAgB,IAAIzC,2BAA2B,CAACuG,IAAI,EAAE,gBAAgB,CAAC,EAAE;IAC1F,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAqcF,OAAO,IAAMgL,MAAM,GAAG7R,yBAAyB;;AAE/C;AACA;AACA;AACA,SAAS1D,IAAI,EAAEwV,MAAM,QAAQ,kBAAkB;AAE/C,eAAenR,cAAc", "ignoreList": []}