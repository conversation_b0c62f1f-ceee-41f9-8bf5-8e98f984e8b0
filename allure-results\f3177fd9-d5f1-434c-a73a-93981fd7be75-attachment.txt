2025-09-18 13:40:59 [INFO]: Initializing browser instance...
2025-09-18 13:41:00 [INFO]: <PERSON><PERSON><PERSON> launched: chrome (headless=false)
2025-09-18 13:41:00 [INFO]: Creating a new browser context for the test...
2025-09-18 13:41:00 [INFO]: Setting up page with authentication...
2025-09-18 13:41:00 [INFO]: Loading authentication state from file...
2025-09-18 13:41:00 [INFO]: Navigating to https://devl-auditportal.sandbox.dealeruplift.net/#/submission-summary
2025-09-18 13:41:07 [INFO]: Navigation completed.
2025-09-18 13:41:07 [INFO]: 🚀 Starting Discount Override All Qual + Copy Data verification...
2025-09-18 13:41:07 [INFO]: 🚀 Starting test for Navigating to Copy Data page
2025-09-18 13:41:14 [INFO]: 🚀 Navigating to Audit Module
2025-09-18 13:41:14 [INFO]: Fetching start date to search Project from Submission Summary page
2025-09-18 13:41:14 [INFO]: Entered start date to search field
2025-09-18 13:41:15 [INFO]: Entered project name to search field
2025-09-18 13:41:15 [INFO]: Clicked on search icon
2025-09-18 13:41:17 [INFO]: Clicked on first entry
2025-09-18 13:41:21 [INFO]: ✅ Navigated to Audit module page
2025-09-18 13:41:24 [INFO]: ✅ Navigated to Audit module page
2025-09-18 13:41:24 [INFO]: Clicked on Utilities dropdown
2025-09-18 13:41:24 [INFO]: Clicked on Copy Data option
2025-09-18 13:41:30 [INFO]: ✅ Navigated to Copy Data page
2025-09-18 13:41:30 [INFO]: ✅ Navigated to Copy Data page
2025-09-18 13:41:31 [INFO]: Clicked on Utilities dropdown
2025-09-18 13:41:31 [INFO]: Clicked on Rule Details option
2025-09-18 13:41:37 [INFO]: ✅ Navigated to Rule Details page
2025-09-18 13:41:37 [INFO]: ✅ Navigated to Rule Details page
2025-09-18 13:41:46 [INFO]: ✅ Clicked header menu button for Store Name
2025-09-18 13:41:47 [INFO]: ✅ Clicked Columns tab
2025-09-18 13:41:48 [INFO]: ✅ Enabled Submission UUID column
2025-09-18 13:41:50 [INFO]: ✅ Closed Columns panel
2025-09-18 13:41:51 [INFO]: 🆔 Submission UUID: c14f14dd-1539-4ad6-8c49-8c8c73f8bc18
2025-09-18 13:41:51 [INFO]: 📅 Imported On: 05-15-2025
2025-09-18 13:41:55 [INFO]: ✅ Switched back to Submission Summary page
2025-09-18 13:41:55 [INFO]: ✅ Searched by Imported Date
2025-09-18 13:41:57 [INFO]: ✅ Entered UUID c14f14dd-1539-4ad6-8c49-8c8c73f8bc18
2025-09-18 13:42:06 [INFO]: ✅ Reopened Audit Page after closing old one
2025-09-18 13:42:15 [INFO]: ✅ Reopened Audit Page after closing old one
2025-09-18 13:42:15 [INFO]: ✅ Navigated to Rule Detail page
2025-09-18 13:42:20 [INFO]: ✅ Clicked Heuristic tab
2025-09-18 13:42:24 [INFO]: ✅ Clicked Heuristic Discount tab
2025-09-18 13:42:27 [INFO]: 📌 Selected RO#: 744663
2025-09-18 13:42:27 [INFO]: 📌 Selected RO#: 744663, Qualified Jobs: A1,B1
2025-09-18 13:42:27 [INFO]: 📌 Selected RO#: 744663, Job Description: 6704- POLICY WORK MECH
2025-09-18 13:42:32 [INFO]: ✅ Applied Override All Qual Jobs (Exclude - Accessory)
2025-09-18 13:42:42 [INFO]: ✅ Update Outcome:  Updated Outcome successfully 
2025-09-18 13:42:52 [INFO]: ✅ Copy process completed with toast:  Updated Outcome successfully 
2025-09-18 13:43:02 [INFO]: ✅ Clicked header menu button for Store Name
2025-09-18 13:43:03 [INFO]: ✅ Clicked Columns tab
2025-09-18 13:43:04 [INFO]: ✅ Enabled Submission UUID column
2025-09-18 13:43:05 [INFO]: ✅ Closed Columns panel
2025-09-18 13:43:06 [INFO]: ✅ Started Copy process
2025-09-18 13:43:07 [INFO]: 🔵 Status (initial): In Progress
2025-09-18 13:43:30 [INFO]: 🟢 Status (completed): Completed
2025-09-18 13:43:38 [INFO]: ✅ Copy process completed with toast:  Updated Outcome successfully 
2025-09-18 13:44:04 [INFO]: ✅ Verified Qualified Job present for RO# 744663: A1
2025-09-18 13:44:04 [INFO]: ✅ Verified Qualified Job present for RO# 744663: B1
2025-09-18 13:44:04 [INFO]: 🔍 Checking row conditions sequentially...
2025-09-18 13:44:04 [INFO]: Row 1 → JobOC='exclude', JobOCReason='exclude - accessory', JobOverride='exclude' (bg=rgb(236, 236, 150)), JobOverrideReason='exclude - accessory'
2025-09-18 13:44:04 [INFO]: Row 2 → IncludeExclude='exclude - accessory' (bg=rgb(220, 204, 97)), Override='job - exclude' (bg=rgb(243, 235, 6))
2025-09-18 13:44:04 [INFO]: ✅ Validation successful: Row 1 and Row 2 meet required conditions.
2025-09-18 13:44:04 [INFO]: ✅ Validation successful for RO# 744663, Qualified Jobs: A1,B1 in discounts tab: Override All Qual Jobs applied correctly with values & background colors.
2025-09-18 13:44:04 [INFO]: ✅ Navigated to Rule Detail page
2025-09-18 13:44:07 [INFO]: ✅ Clicked Heuristic tab
2025-09-18 13:44:12 [INFO]: ✅ Clicked Heuristic Discount tab
2025-09-18 13:44:21 [INFO]: ✅ Applied Job Override (None)
2025-09-18 13:44:31 [INFO]: ✅ Update Outcome:  Updated Outcome successfully 
2025-09-18 13:44:41 [INFO]: ✅ Copy process completed with toast:  Updated Outcome successfully 
2025-09-18 13:44:55 [INFO]: ✅ Clicked header menu button for Store Name
2025-09-18 13:44:56 [INFO]: ✅ Clicked Columns tab
2025-09-18 13:44:57 [INFO]: ✅ Enabled Submission UUID column
2025-09-18 13:44:58 [INFO]: ✅ Closed Columns panel
2025-09-18 13:44:59 [INFO]: ✅ Started Copy process
2025-09-18 13:45:00 [INFO]: 🔵 Status (initial): In Progress
2025-09-18 13:45:22 [INFO]: 🟢 Status (completed): Completed
2025-09-18 13:45:31 [INFO]: ✅ Copy process completed with toast:  Updated Outcome successfully 
2025-09-18 13:45:33 [INFO]: Closing browser context after test completion...
2025-09-18 13:45:38 [INFO]: Test Completed: Performing cleanup...
2025-09-18 13:45:38 [INFO]: Closing browser instance...
2025-09-18 13:45:38 [INFO]: Browser closed successfully.
