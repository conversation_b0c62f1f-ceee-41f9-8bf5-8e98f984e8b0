{"version": 3, "file": "utils.js", "names": ["_allure<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "AFTER_HOOKS_ROOT_STEP_TITLE", "exports", "BEFORE_HOOKS_ROOT_STEP_TITLE", "statusToAllureStats", "status", "expectedStatus", "Status", "SKIPPED", "BROKEN", "PASSED", "FAILED", "isDescendantOfStepWithTitle", "step", "title", "parent", "isAfterHookStep", "isBeforeHookStep", "diffEndRegexp", "normalizeHookTitle", "replace"], "sources": ["../../src/utils.ts"], "sourcesContent": ["import type { TestStatus } from \"@playwright/test\";\nimport type { TestStep } from \"@playwright/test/reporter\";\nimport { Status } from \"allure-js-commons\";\n\nexport const AFTER_HOOKS_ROOT_STEP_TITLE = \"After Hooks\";\n\nexport const BEFORE_HOOKS_ROOT_STEP_TITLE = \"Before Hooks\";\n\nexport const statusToAllureStats = (status: TestStatus, expectedStatus: TestStatus): Status => {\n  if (status === \"skipped\") {\n    return Status.SKIPPED;\n  }\n\n  if (status === \"timedOut\") {\n    return Status.BROKEN;\n  }\n\n  if (status === expectedStatus) {\n    return Status.PASSED;\n  }\n\n  return Status.FAILED;\n};\n\nexport const isDescendantOfStepWithTitle = (step: TestStep, title: string): boolean => {\n  let parent = step.parent;\n\n  while (parent) {\n    if (parent.title === title) {\n      return true;\n    }\n\n    parent = parent.parent;\n  }\n\n  return false;\n};\n\nexport const isAfterHookStep = (step: TestStep) => isDescendantOfStepWithTitle(step, AFTER_HOOKS_ROOT_STEP_TITLE);\n\nexport const isBeforeHookStep = (step: TestStep) => isDescendantOfStepWithTitle(step, BEFORE_HOOKS_ROOT_STEP_TITLE);\n\nexport const diffEndRegexp = /-((expected)|(diff)|(actual))\\.png$/;\n\nexport const normalizeHookTitle = (title: string) => {\n  return title.replace(/^[aA]ttach\\s\"(.+)\"$/, \"$1\");\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,gBAAA,GAAAC,OAAA;AAEO,IAAMC,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,GAAG,aAAa;AAEjD,IAAME,4BAA4B,GAAAD,OAAA,CAAAC,4BAAA,GAAG,cAAc;AAEnD,IAAMC,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAIC,MAAkB,EAAEC,cAA0B,EAAa;EAC7F,IAAID,MAAM,KAAK,SAAS,EAAE;IACxB,OAAOE,uBAAM,CAACC,OAAO;EACvB;EAEA,IAAIH,MAAM,KAAK,UAAU,EAAE;IACzB,OAAOE,uBAAM,CAACE,MAAM;EACtB;EAEA,IAAIJ,MAAM,KAAKC,cAAc,EAAE;IAC7B,OAAOC,uBAAM,CAACG,MAAM;EACtB;EAEA,OAAOH,uBAAM,CAACI,MAAM;AACtB,CAAC;AAEM,IAAMC,2BAA2B,GAAAV,OAAA,CAAAU,2BAAA,GAAG,SAA9BA,2BAA2BA,CAAIC,IAAc,EAAEC,KAAa,EAAc;EACrF,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;EAExB,OAAOA,MAAM,EAAE;IACb,IAAIA,MAAM,CAACD,KAAK,KAAKA,KAAK,EAAE;MAC1B,OAAO,IAAI;IACb;IAEAC,MAAM,GAAGA,MAAM,CAACA,MAAM;EACxB;EAEA,OAAO,KAAK;AACd,CAAC;AAEM,IAAMC,eAAe,GAAAd,OAAA,CAAAc,eAAA,GAAG,SAAlBA,eAAeA,CAAIH,IAAc;EAAA,OAAKD,2BAA2B,CAACC,IAAI,EAAEZ,2BAA2B,CAAC;AAAA;AAE1G,IAAMgB,gBAAgB,GAAAf,OAAA,CAAAe,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIJ,IAAc;EAAA,OAAKD,2BAA2B,CAACC,IAAI,EAAEV,4BAA4B,CAAC;AAAA;AAE5G,IAAMe,aAAa,GAAAhB,OAAA,CAAAgB,aAAA,GAAG,qCAAqC;AAE3D,IAAMC,kBAAkB,GAAAjB,OAAA,CAAAiB,kBAAA,GAAG,SAArBA,kBAAkBA,CAAIL,KAAa,EAAK;EACnD,OAAOA,KAAK,CAACM,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;AACnD,CAAC", "ignoreList": []}