{"version": 3, "file": "runtime.js", "names": ["_NoopTestRuntime", "require", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ALLURE_TEST_RUNTIME_KEY", "setGlobalTestRuntime", "runtime", "globalThis", "exports", "getGlobalTestRuntimeFunction", "getGlobalTestRuntime", "testRuntime", "_testRuntime", "noopRuntime", "getGlobalTestRuntimeWithAutoconfig", "_testRuntime2", "pwAutoconfigModuleName", "specifier", "Promise", "concat", "then", "s", "_getGlobalTestRuntime", "_getGlobalTestRuntime2", "catch"], "sources": ["../../../../src/sdk/runtime/runtime.ts"], "sourcesContent": ["import { noopRuntime } from \"./NoopTestRuntime.js\";\nimport type { TestRuntime } from \"./types.js\";\n\nconst ALLURE_TEST_RUNTIME_KEY = \"allureTestRuntime\";\n\nexport const setGlobalTestRuntime = (runtime: TestRuntime) => {\n  (globalThis as any)[ALLURE_TEST_RUNTIME_KEY] = () => runtime;\n};\n\nconst getGlobalTestRuntimeFunction = () => {\n  return (globalThis as any)?.[ALLURE_TEST_RUNTIME_KEY] as (() => TestRuntime | undefined) | undefined;\n};\n\nexport const getGlobalTestRuntime = (): TestRuntime => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  return noopRuntime;\n};\n\nexport const getGlobalTestRuntimeWithAutoconfig = (): TestRuntime | Promise<TestRuntime> => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  // protection from bundlers tree-shaking visiting (webpack, rollup)\n  const pwAutoconfigModuleName = \"allure-playwright/autoconfig\";\n\n  return import(pwAutoconfigModuleName)\n    .then(() => getGlobalTestRuntimeFunction()?.() ?? noopRuntime)\n    .catch(() => noopRuntime);\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AAAmD,SAAAC,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGnD,IAAMmB,uBAAuB,GAAG,mBAAmB;AAE5C,IAAMC,oBAAoB,GAAIC,OAAoB,IAAK;EAC3DC,UAAU,CAASH,uBAAuB,CAAC,GAAG,MAAME,OAAO;AAC9D,CAAC;AAACE,OAAA,CAAAH,oBAAA,GAAAA,oBAAA;AAEF,IAAMI,4BAA4B,GAAGA,CAAA,KAAM;EACzC,OAAQF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAWH,uBAAuB,CAAC;AACvD,CAAC;AAEM,IAAMM,oBAAoB,GAAGA,CAAA,KAAmB;EACrD,IAAMC,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAC,YAAA;IACf,QAAAA,YAAA,GAAOD,WAAW,CAAC,CAAC,cAAAC,YAAA,cAAAA,YAAA,GAAIC,4BAAW;EACrC;EAEA,OAAOA,4BAAW;AACpB,CAAC;AAACL,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAEK,IAAMI,kCAAkC,GAAGA,CAAA,KAA0C;EAC1F,IAAMH,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAI,aAAA;IACf,QAAAA,aAAA,GAAOJ,WAAW,CAAC,CAAC,cAAAI,aAAA,cAAAA,aAAA,GAAIF,4BAAW;EACrC;;EAEA;EACA,IAAMG,sBAAsB,GAAG,8BAA8B;EAE7D,OAAO,CAAAC,SAAA,QAAAC,OAAA,CAAA/B,CAAA,IAAAA,CAAA,IAAAgC,MAAA,CAAAF,SAAA,IAAAG,IAAA,CAAAC,CAAA,IAAAtC,uBAAA,CAAAD,OAAA,CAAAuC,CAAA,KAAOL,sBAAsB,EACjCI,IAAI,CAAC;IAAA,IAAAE,qBAAA,EAAAC,sBAAA;IAAA,QAAAD,qBAAA,IAAAC,sBAAA,GAAMd,4BAA4B,CAAC,CAAC,cAAAc,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,cAAAD,qBAAA,cAAAA,qBAAA,GAAIT,4BAAW;EAAA,EAAC,CAC7DW,KAAK,CAAC,MAAMX,4BAAW,CAAC;AAC7B,CAAC;AAACL,OAAA,CAAAM,kCAAA,GAAAA,kCAAA", "ignoreList": []}