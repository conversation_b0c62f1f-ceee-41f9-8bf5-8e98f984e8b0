import {
  test as baseTest,
  chromium,
  firefox,
  webkit,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  BrowserContext,
} from "@playwright/test";
import dotenv from "dotenv";
import Logger from "../utils/logger-util";
import fs from "fs";
import path from "path";

dotenv.config();
const logger = new Logger();

const browserType: string = process.env.browser || "chromium"; // Default to chromium
const authStatePath = path.join(__dirname, "../auth.json");

// ✅ Respect HEADLESS from .env (default = headless true)
const isHeadless = process.env.HEADLESS?.toLowerCase() !== "false";

let browser: Browser | null = null; // Persist browser instance across tests

const test = baseTest.extend<{
  browserInstance: Browser;
  context: BrowserContext;
  pageWithAuth: Page;
}>({
  browserInstance: async ({}, use) => {
    logger.info("Initializing browser instance...");

    if (!browser) {
      const launchOptions = { headless: isHeadless };

      if (browserType === "chrome" || browserType === "chromium") {
        browser = await chromium.launch(launchOptions);
      } else if (browserType === "firefox") {
        browser = await firefox.launch(launchOptions);
      } else if (browserType === "webkit") {
        browser = await webkit.launch(launchOptions);
      } else {
        throw new Error(`Unsupported browser type: ${browserType}`);
      }

      logger.info(`Browser launched: ${browserType} (headless=${isHeadless})`);
    }

    await use(browser);
  },

  context: async ({ browserInstance }, use) => {
    logger.info("Creating a new browser context for the test...");

    if (!browserInstance) {
      throw new Error("Browser instance is not initialized.");
    }

    // ✅ Apply viewport globally for all pages in this context
    const context = await browserInstance.newContext({
      viewport: { width: 1920, height: 1080 },
    });

    await use(context);

    logger.info("Closing browser context after test completion...");
    await context.close();
  },

  pageWithAuth: async ({ context }, use) => {
    logger.info("Setting up page with authentication...");

    const page: Page = await context.newPage();

    if (fs.existsSync(authStatePath)) {
      logger.info("Loading authentication state from file...");
      const cookies = JSON.parse(fs.readFileSync(authStatePath, "utf8"));
      await context.addCookies(cookies);
    } else {
      logger.error(
        "Authentication state file not found. Run `auth.ts` first with `npx txt auth.ts`"
      );
      throw new Error(
        "Authentication state file not found. Run `auth.ts` first with `npx txt auth.ts`"
      );
    }

    const url = process.env.url || "";
    logger.info(`Navigating to ${url}`);
    await page.goto(url);

    logger.info("Navigation completed.");
    await use(page);
  },
});

test.afterAll(async () => {
  logger.info("Test Completed: Performing cleanup...");
  if (browser) {
    logger.info("Closing browser instance...");
    await browser.close();
    browser = null; // Reset for next run
    logger.info("Browser closed successfully.");
  }
});

export default test;
