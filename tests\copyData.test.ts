import test from "../fixture/fixtures.ts";
import Logger from "../utils/logger-util.ts";
import CopyDataPage from "../pages/triggerEmail.page.ts";
import dotenv from "dotenv";

dotenv.config();

const logger = new Logger();

test("TC-01- Check whether the Copy Data UI is properly loaded upon redirection from the Audit Module UI.", async ({
  pageWithAuth,
}) => {
  const copyDataTest = new CopyDataPage(pageWithAuth);
  await copyDataTest.navigateToCopyData();
});
test.skip(
  "TC-23- Check whether the 'Job Override' functionality from the Alignment sub-tab in the Heuristic tab in the Rule Details UI is included in the Copy Data functionality.",
  {
    annotation: {
      type: "skip reason",
      description: "Data is not available",
    },
  },
  async ({ pageWithAuth }) => {
    test.setTimeout(480000);
    const copyDataTest = new CopyDataPage(pageWithAuth);
    await copyDataTest.navigateToCopyData();
  }
);
