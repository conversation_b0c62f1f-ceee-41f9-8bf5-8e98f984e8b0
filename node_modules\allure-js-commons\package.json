{"name": "allure-js-commons", "version": "3.4.1", "description": "Allure JS Commons", "keywords": ["allure", "playwright", "cypress", "mocha", "vitest", "jest", "jasmine", "newman", "postman", "codeceptjs", "junit", "test", "report", "reporter", "testing", "testops"], "homepage": "https://allurereport.org/", "repository": {"type": "git", "url": "https://github.com/allure-framework/allure-js.git", "directory": "packages/allure-js-commons"}, "license": "Apache-2.0", "author": {"name": "Qameta Software", "email": "<EMAIL>", "url": "https://qameta.io/"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "default": "./dist/cjs/index.js"}, "./sdk": {"types": "./dist/types/sdk/index.d.ts", "import": "./dist/esm/sdk/index.js", "require": "./dist/cjs/sdk/index.js", "default": "./dist/cjs/sdk/index.js"}, "./sdk/reporter": {"types": "./dist/types/sdk/reporter/index.d.ts", "import": "./dist/esm/sdk/reporter/index.js", "require": "./dist/cjs/sdk/reporter/index.js", "default": "./dist/cjs/sdk/reporter/index.js"}, "./sdk/runtime": {"types": "./dist/types/sdk/runtime/index.d.ts", "import": "./dist/esm/sdk/runtime/index.js", "require": "./dist/cjs/sdk/runtime/index.js", "default": "./dist/cjs/sdk/runtime/index.js"}}, "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/types/index.d.ts", "files": ["dist"], "scripts": {"clean": "rimraf ./dist", "compile": "run-s 'compile:*'", "compile:esm": "babel --config-file ./babel.esm.json ./src --out-dir ./dist/esm --extensions '.ts' --source-maps", "compile:cjs": "babel --config-file ./babel.cjs.json ./src --out-dir ./dist/cjs --extensions '.ts' --source-maps", "compile:types": "tsc", "compile:fixup": "node ./scripts/fixup.mjs", "lint": "eslint ./src ./test --ext .ts", "lint:fix": "eslint ./src ./test --ext .ts --fix", "test": "vitest run"}, "dependencies": {"md5": "^2.3.0"}, "devDependencies": {"@babel/cli": "^7.28.0", "@babel/core": "^7.28.0", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@stylistic/eslint-plugin": "^2.6.1", "@types/babel__core": "^7.20.5", "@types/babel__preset-env": "^7.10.0", "@types/eslint": "^8.56.11", "@types/md5": "^2", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^54.0.0", "eslint-plugin-n": "^17.10.1", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-prefer-arrow": "^1.2.3", "npm-run-all2": "^8.0.0", "rimraf": "^6.0.0", "typescript": "^5.2.2", "vitest": "^3.2.4"}, "peerDependencies": {"allure-playwright": "3.4.1"}, "peerDependenciesMeta": {"allure-playwright": {"optional": true}}}