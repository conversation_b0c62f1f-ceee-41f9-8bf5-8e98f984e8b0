function _tsRewriteRelativeImportExtensions(t, e) { return "string" == typeof t && /^\.\.?\//.test(t) ? t.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+)?)\.([cm]?)ts$/i, function (t, s, r, n, o) { return s ? e ? ".jsx" : ".js" : !r || n && o ? r + n + "." + o.toLowerCase() + "js" : t; }) : t; }
import { noopRuntime } from "./NoopTestRuntime.js";
var ALLURE_TEST_RUNTIME_KEY = "allureTestRuntime";
export var setGlobalTestRuntime = runtime => {
  globalThis[ALLURE_TEST_RUNTIME_KEY] = () => runtime;
};
var getGlobalTestRuntimeFunction = () => {
  return globalThis === null || globalThis === void 0 ? void 0 : globalThis[ALLURE_TEST_RUNTIME_KEY];
};
export var getGlobalTestRuntime = () => {
  var testRuntime = getGlobalTestRuntimeFunction();
  if (testRuntime) {
    var _testRuntime;
    return (_testRuntime = testRuntime()) !== null && _testRuntime !== void 0 ? _testRuntime : noopRuntime;
  }
  return noopRuntime;
};
export var getGlobalTestRuntimeWithAutoconfig = () => {
  var testRuntime = getGlobalTestRuntimeFunction();
  if (testRuntime) {
    var _testRuntime2;
    return (_testRuntime2 = testRuntime()) !== null && _testRuntime2 !== void 0 ? _testRuntime2 : noopRuntime;
  }

  // protection from bundlers tree-shaking visiting (webpack, rollup)
  var pwAutoconfigModuleName = "allure-playwright/autoconfig";
  return import(_tsRewriteRelativeImportExtensions(pwAutoconfigModuleName)).then(() => {
    var _getGlobalTestRuntime, _getGlobalTestRuntime2;
    return (_getGlobalTestRuntime = (_getGlobalTestRuntime2 = getGlobalTestRuntimeFunction()) === null || _getGlobalTestRuntime2 === void 0 ? void 0 : _getGlobalTestRuntime2()) !== null && _getGlobalTestRuntime !== void 0 ? _getGlobalTestRuntime : noopRuntime;
  }).catch(() => noopRuntime);
};
//# sourceMappingURL=runtime.js.map